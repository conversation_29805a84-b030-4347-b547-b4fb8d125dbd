import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { View, ScrollView, StyleSheet, BackHandler, TouchableOpacity, ActivityIndicator } from 'react-native';
import { Button, Text, useTheme, Searchbar, List, IconButton, Surface } from 'react-native-paper';
import { useIsFocused, useNavigation, useFocusEffect } from '@react-navigation/native';
import { useAppContext } from './store/AppContext';
import AsyncStorage from '@react-native-async-storage/async-storage';
import BuyNowModal from './components/BuyNowModal';
import apiClient from './services/ApiClient';
import { useExamContext } from './store/ExamContext';
import { useQnAContext } from './store/QnAContext';
import { usePurchase } from './store/PurchaseContext';
import { useLogin } from './store/LoginContext';
import FixedFontButton from './components/FixedFontButton';

/**
 * Fetches exam data from the API
 * @returns {Promise<Array>} Array of exam objects
 */
const fetchExams = async () => {
  try {
    return await apiClient.getExams();
  } catch (error) {
    console.error('Error fetching exams:', error);
    return [];
  }
};

/**
 * Formats the exam update date for display
 * @param {Object} exam - The exam object
 * @returns {string} Formatted date string
 */
const getExamDescription = (exam) => {
  const dateString = exam.date_updated || exam.timestamp_updated || exam.timestamp;
  if (!dateString) return 'N/A';

  const date = new Date(dateString);
  return date.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
  });
};

/**
 * Finds purchase information for a specific exam
 * @param {string|number} examId - The exam ID to find
 * @param {Array} purchases - Array of purchase objects
 * @returns {Object|null} Purchase object or null if not found
 */
const getPurchaseInfo = (examId, purchases) => {
  if (!purchases?.length || !examId) return null;
  return purchases.find(p => String(p.examId) === String(examId));
};

const ProductSelect = ({ route }) => {
  // Context hooks
  const { loadQnA } = useQnAContext();
  const { colors } = useTheme();
  const navigation = useNavigation();
  const { setSelectedOption } = useAppContext();
  const { selectExam, selectedExam } = useExamContext();
  const { purchases, subscriptionInfo, getSubscriptionsInfo, storeSubscriptionStatus } = usePurchase();
  const { isLoggedIn } = useLogin() || {};
  const isFocused = useIsFocused();

  // Check if this is the initial screen (forced selection)
  const isInitialScreen = route?.name === 'ProductSelect';

  // UI state
  const [searchText, setSearchText] = useState('');
  const [expandedCategoryIds, setExpandedCategoryIds] = useState([]);
  const [isSingleVendor, setIsSingleVendor] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  // Product selection state
  const [selectedProductId, setSelectedProductId] = useState(null);
  const [selectedProductIdLoading, setSelectedProductIdLoading] = useState(null);

  // Purchase modal state
  const [buyNowModalVisible, setBuyNowModalVisible] = useState(false);
  const [selectedPlan, setSelectedPlan] = useState('weekly');
  const [selectedProduct, setSelectedProduct] = useState(null);

  // Data state
  const [vendors, setVendors] = useState([]);

  // Handle product selection
  const handleProductPress = useCallback((productId) => {
    setSelectedProductId(prevId => prevId === productId ? null : productId);
  }, []);

  // Load exams data and handle initial setup
  useEffect(() => {
    const loadExams = async () => {
      setIsLoading(true);
      try {
        // Fetch exams data
        const exams = await fetchExams();
        if (!exams?.length) {
          setIsLoading(false);
          return;
        }

        // Get stored product from AsyncStorage
        let storedProduct = await getStoredProduct(exams);

        // If we have a selected exam from context, use that instead
        if (selectedExam?.id) {
          const matchingProduct = exams.find(e => e.id === selectedExam.id);
          if (matchingProduct) {
            storedProduct = matchingProduct;
          }
        }

        // Process and organize exams by vendor
        const { vendorCategories, updatedVendorMap } = organizeExamsByVendor(exams, storedProduct);

        // Update state with processed data
        setVendors(vendorCategories);
        setIsSingleVendor(vendorCategories.length === 1);

        // Set expanded categories based on stored product
        updateExpandedCategories(vendorCategories, storedProduct);

        // Restore selected product ID if we have a stored product
        if (storedProduct) {
          setSelectedProductId(storedProduct.id);
        }
      } catch (error) {
        console.error('Loading error:', error);
      } finally {
        setIsLoading(false);
      }
    };

    // Helper function to get stored product
    const getStoredProduct = async (exams) => {
      try {
        const storedProductString = await AsyncStorage.getItem('selectedProduct');
        if (!storedProductString) return null;

        const storedProduct = JSON.parse(storedProductString);
        // Validate that the stored product still exists in our exams
        if (!exams.some(e => e.id === storedProduct.id)) {
          await AsyncStorage.removeItem('selectedProduct');
          return null;
        }

        return storedProduct;
      } catch (error) {
        console.error('Error getting stored product:', error);
        return null;
      }
    };

    // Helper function to organize exams by vendor
    const organizeExamsByVendor = (exams, storedProduct) => {
      // Group by vendor
      const vendorMap = exams.reduce((acc, exam) => {
        const vendor = exam.vendor_name;
        acc[vendor] = acc[vendor] || [];
        acc[vendor].push(exam);
        return acc;
      }, {});

      // Move stored product to top of its vendor list if it exists
      if (storedProduct) {
        Object.keys(vendorMap).forEach(vendorName => {
          const products = vendorMap[vendorName];
          const index = products.findIndex(p => p.id === storedProduct.id);
          if (index !== -1) {
            const [selectedProduct] = products.splice(index, 1);
            products.unshift(selectedProduct);
          }
        });
      }

      // Convert to array format for UI
      const vendorCategories = Object.entries(vendorMap).map(([name, products]) => ({
        id: name,
        name,
        products,
      }));

      return { vendorCategories, updatedVendorMap: vendorMap };
    };

    // Helper function to update expanded categories
    const updateExpandedCategories = (vendorCategories, storedProduct) => {
      if (storedProduct && vendorCategories.length > 1) {
        const vendorCategory = vendorCategories.find(cat => cat.name === storedProduct.vendor_name);
        if (vendorCategory) {
          setExpandedCategoryIds([vendorCategory.id]);
          return;
        }
      }

      // Default expansion behavior
      setExpandedCategoryIds(
        vendorCategories.length === 1 ? vendorCategories.map(cat => cat.id) : []
      );
    };

    loadExams();
  }, []);

  // Handle back button
  useEffect(() => {
    const onBackPress = () => {
      // If this is the initial screen, prevent back action
      if (isInitialScreen) {
        return true; // Prevent default back action
      }
      return isFocused;
    };
    const backHandler = BackHandler.addEventListener('hardwareBackPress', onBackPress);
    return () => backHandler.remove();
  }, [isFocused, isInitialScreen]);

  // Refresh the UI when the component is focused or login status changes
  /* useFocusEffect(
    useCallback(() => {
      // Force a quick re-render to update purchase status indicators
      // This is more efficient than a full data reload
      if (vendors.length > 0) {
        setIsLoading(true);
        const timer = setTimeout(() => setIsLoading(false), 50);
        return () => clearTimeout(timer);
      }
    }, [isLoggedIn, vendors.length])
  ); */

  // Toggle category expansion
  const handleAccordionPress = useCallback((categoryId) => {
    setExpandedCategoryIds(prevIds =>
      prevIds.includes(categoryId)
        ? prevIds.filter(id => id !== categoryId)
        : [...prevIds, categoryId]
    );
  }, []);

  /**
   * Filter categories and products based on search text
   * This is memoized to avoid recalculating on every render
   */
  const filteredCategories = useMemo(() => {
    // If no search text, return all vendors
    if (!searchText || !vendors.length) return vendors;

    const query = searchText.toLowerCase().trim();

    // If query is empty after trimming, return all vendors
    if (!query) return vendors;

    return vendors
      // First filter categories that match the query or have products that match
      .filter(cat => {
        // For single vendor, always include the category but filter its products
        if (isSingleVendor) return true;

        // Check if category name matches
        if (cat.name.toLowerCase().includes(query)) return true;

        // Check if any product in the category matches
        return cat.products.some(p =>
          p.exam_name.toLowerCase().includes(query) ||
          p.exam_code.toLowerCase().includes(query)
        );
      })
      // Then for each matching category, filter its products
      .map(cat => ({
        ...cat,
        products: cat.products.filter(p =>
          p.exam_name.toLowerCase().includes(query) ||
          p.exam_code.toLowerCase().includes(query)
        )
      }));
  }, [searchText, vendors, isSingleVendor]);


  /**
   * Render product cards - memoized to avoid recreating on every render
   */
  // State to track subscription status and loading state
  const [productSubscriptions, setProductSubscriptions] = useState({});
  const [isSubscriptionChecking, setIsSubscriptionChecking] = useState(false);

  // Effect to update subscription status when products or login state changes
  useEffect(() => {
    console.log('[PRODUCT_SELECT] Subscription check effect triggered', {
      vendorsLength: vendors.length,
      isLoggedIn
    });

    let isMounted = true;

    const updateSubscriptions = async () => {
      console.log('[PRODUCT_SELECT] Starting subscription check');
      setIsSubscriptionChecking(true);

      if (!vendors.length) {
        console.log('[PRODUCT_SELECT] No vendors, skipping subscription check');
        setIsSubscriptionChecking(false);
        return;
      }

      // Create mapping of product IDs to exam codes
      const productExamMap = {};
      const examCodes = [];

      vendors.forEach(vendor => {
        vendor.products.forEach(product => {
          console.log(`[PRODUCT_SELECT] Mapping product ${product.id} to exam code ${product.exam_code}`);
          productExamMap[product.id] = product.exam_code;
          examCodes.push(product.exam_code);
        });
      });

      console.log('[PRODUCT_SELECT] Exam codes to check:', examCodes);
      console.log('[PRODUCT_SELECT] Product to exam code mapping:', productExamMap);

      // If not logged in, set all subscriptions to false
      if (!isLoggedIn) {
        console.log('[PRODUCT_SELECT] User not logged in, setting all subscriptions to false');
        if (isMounted) {
          const allFalse = {};
          vendors.forEach(vendor => {
            vendor.products.forEach(product => {
              allFalse[product.id] = false;
            });
          });
          setProductSubscriptions(allFalse);
          setIsSubscriptionChecking(false);
        }
        return;
      }

      try {
        console.log('[PRODUCT_SELECT] Checking subscription status for exams:', examCodes);
        const subscriptionResults = await getSubscriptionsInfo(examCodes, true);
        console.log('[PRODUCT_SELECT] Subscription check results:', subscriptionResults);

        if (isMounted) {
          const newSubscriptions = {};

          // Update subscription status for each product
          vendors.forEach(vendor => {
            vendor.products.forEach(product => {
              const examCode = productExamMap[product.id];
              const isActive = subscriptionResults && subscriptionResults[examCode] || false;
              //console.log(`[PRODUCT_SELECT] Product ${product.id} (${examCode}) isPremium: ${isActive}`);
              newSubscriptions[product.id] = isActive;
            });
          });

          console.log('[PRODUCT_SELECT] New subscriptions state:', newSubscriptions);
          setProductSubscriptions(newSubscriptions);
          setIsSubscriptionChecking(false);
        }
      } catch (error) {
        console.error('[PRODUCT_SELECT] Error checking subscriptions:', error);
        if (isMounted) {
          console.log('[PRODUCT_SELECT] Falling back to all false due to error');
          // Fallback to all false if error occurs
          const allFalse = {};
          vendors.forEach(vendor => {
            vendor.products.forEach(product => {
              allFalse[product.id] = false;
            });
          });
          setProductSubscriptions(allFalse);
          setIsSubscriptionChecking(false);
        }
      }
    };

    updateSubscriptions();

    return () => {
      console.log('[PRODUCT_SELECT] Cleaning up subscription check');
      isMounted = false;
      setIsSubscriptionChecking(false);
    };
  }, [vendors, isLoggedIn]);

  const renderProducts = useCallback((products) => {
    if (!products?.length) return null;

    return products.map((product) => {
      const isSelected = selectedProductId === product.id;
      const isLoading = selectedProductIdLoading === product.id;
      const subscriptionData = productSubscriptions[product.id] || {};
      const hasActiveSubscription = subscriptionData.isActive || false;
      const subscriptionDetails = subscriptionData.details || [];

      /* console.log('[PRODUCT_SELECT] Premium status:', {
        examId: product.id,
        isPremium: hasActiveSubscription,
        details: subscriptionDetails,
        checkTimestamp: new Date().toISOString()
      }); */

      // Card style based on selection
      const cardStyle = [
        styles.productCard,
        {
          backgroundColor: colors.surface,
          borderColor: isSelected ? colors.primary : 'rgba(0,0,0,0.08)'
        }
      ];

      // Title style based on selection
      const titleStyle = [
        styles.productName,
        { color: isSelected ? colors.primary : colors.onSurface }
      ];

      // Render purchase info if applicable
      const renderPurchaseInfo = () => {
        if (!hasActiveSubscription) return null;

        // Format date for display
        const formatDate = (dateString) => {
          if (!dateString) return 'N/A';
          const date = new Date(dateString);
          return date.toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric'
          });
        };

        // First try to use subscription details
        if (subscriptionDetails.length > 0) {
          // Get the most recent subscription
          const activeSub = subscriptionDetails.find(sub => sub.isActive) || subscriptionDetails[0];

          return (
            <View style={[{ flexDirection: 'row', gap: 8, alignItems: 'center' }]}>
              <Text style={[styles.purchaseInfoText, { color: colors.primary }]}
                allowFontScaling={false}>
                Purchased {formatDate(activeSub.purchaseDate)}
              </Text>
              <View style={[styles.separator, { backgroundColor: colors.onSurface }]} />
              <Text style={[styles.purchaseInfoText, { color: colors.primary }]}
                allowFontScaling={false}>
                {activeSub.willRenew ? 'Renews' : 'Expires'} {formatDate(activeSub.expiresDate)}
                {subscriptionDetails.length > 1 && ` (${subscriptionDetails.length} active)`}
              </Text>
            </View>
          );
        }
        return null;
      };

      return (
        <TouchableOpacity
          key={product.id}
          style={cardStyle}
          onPress={() => handleProductPress(product.id)}
          activeOpacity={0.9}
        >
          <View style={styles.productContent}>
            <View style={styles.productHeader}>
              <View style={styles.titleContainer}>
                <View style={styles.titleRow}>
                  <Text variant="titleMedium" style={titleStyle} numberOfLines={2} ellipsizeMode="tail"
                    allowFontScaling={false}>
                    {product.exam_name} ({product.exam_code})
                  </Text>

                  {/* Active indicator for upgraded exams */}
                  {hasActiveSubscription && (
                    <View style={[styles.activeIndicator, { backgroundColor: colors.primary }]}>
                      <Text style={styles.activeText}
                        allowFontScaling={false}>Sub.</Text>
                    </View>
                  )}
                </View>

                <View style={styles.metaContainer}>
                  <View style={styles.metaItem}>
                    <Text style={[styles.metaText, { color: colors.primary }]}
                      allowFontScaling={false}>
                      {product.total_questions} Questions
                    </Text>
                    <View style={[styles.separator, { backgroundColor: colors.onSurface }]} />
                    <Text style={[styles.metaText, { color: colors.secondary }]}
                      allowFontScaling={false}>
                      Updated: {getExamDescription(product)}
                    </Text>
                  </View>

                  {/* Show purchase info for active exams */}
                  {hasActiveSubscription && (
                    <View style={styles.purchaseInfoContainer}>
                      {renderPurchaseInfo()}
                    </View>
                  )}
                </View>
              </View>

              {isSelected && (
                <IconButton
                  icon="check-circle"
                  color={colors.primary}
                  size={24}
                />
              )}
            </View>

            {isSelected && (
              <View style={styles.actionsContainer}>
                {hasActiveSubscription ? (
                  <FixedFontButton
                    mode="contained"
                    icon={selectedProductIdLoading ? '' : "crown"}
                    style={styles.actionButton}
                    contentStyle={styles.buttonContent}
                    labelStyle={{ fontSize: 12, color: '#FFFFFF' }}
                    onPress={() => handleProductSelect(product, false)}
                    loading={selectedProductIdLoading}
                    disabled={isLoading}
                  >
                    <Text allowFontScaling={false}>
                      {selectedProductIdLoading ? 'Loading...' : 'Premium Access'}
                    </Text>
                  </FixedFontButton>
                ) : (
                  <>
                    <FixedFontButton
                      mode="outlined"
                      icon="test-tube"
                      style={styles.actionButton}
                      onPress={() => handleProductSelect(product, true)}
                      contentStyle={styles.buttonContent}
                      labelStyle={styles.buttonLabel}
                      loading={isLoading}
                      disabled={isLoading}
                    >
                      {isLoading ? 'Loading...' : 'Free Sample'}
                    </FixedFontButton>
                    <FixedFontButton
                      mode="contained"
                      icon="lock-open"
                      style={styles.actionButton}
                      onPress={() => handleBuyNow(product)}
                      contentStyle={styles.buttonContent}
                      labelStyle={{ fontSize: 12, color: '#FFFFFF' }}
                    >Full Access
                    </FixedFontButton>
                  </>
                )}
              </View>
            )}
          </View>
        </TouchableOpacity>
      );
    });
  }, [
    colors,
    selectedProductId,
    selectedProductIdLoading,
    isLoggedIn,
    productSubscriptions,
    subscriptionInfo,
    purchases,
    handleProductPress,
    handleProductSelect,
    handleBuyNow
  ]);
  const handleProductSelect = useCallback(async (product, is_free) => {
    // Prevent multiple clicks while loading
    if (selectedProductIdLoading) {
      console.log('Request already in progress');
      return;
    }

    setSelectedProductIdLoading(product.id);
    try {
      console.log(`Selected ${is_free ? 'free sample' : 'premium'} exam code:`, product.exam_code);
      await AsyncStorage.setItem('selectedProduct', JSON.stringify(product));
      setSelectedOption(product);

      await selectExam(product);

      //await loadQnA(product.exam_code, is_free, product.id);
      console.log('Updating subscription status for exam code: ', product.exam_code, 'is_free:', is_free)
      await storeSubscriptionStatus(product.exam_code, !is_free)

      // Use reset instead of navigate to ensure we go back to the main tabs
      navigation.reset({
        index: 0,
        routes: [{ name: 'MainTabs' }],
      });
    } catch (error) {
      console.error(`${is_free ? 'Free sample' : 'Premium access'} selection error:`, error.message);
    } finally {
      setSelectedProductIdLoading(null);
    }
  }, [navigation, selectExam, setSelectedOption, selectedProductIdLoading]);

  const handleBuyNow = (product) => {
    setSelectedProduct(product);
    setBuyNowModalVisible(true);
  };

  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      {!isInitialScreen && (
        <IconButton
          icon="close"
          size={24}
          onPress={() => navigation.goBack()}
          style={styles.closeIcon}
          color={colors.text}
        />
      )}

      <View style={[styles.searchWrapper, isInitialScreen && styles.searchWrapperInitial]}>
        <Searchbar
          placeholder="Search exams..."
          onChangeText={setSearchText}
          value={searchText}
          style={styles.searchInput}
          inputStyle={styles.searchInputText}
          elevation={2}
          allowFontScaling={false}
        />
      </View>

      <ScrollView contentContainerStyle={styles.scrollContent}>
        {isLoading || isSubscriptionChecking ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator animating={true} color={colors.primary} size="large" />
            <Text style={[styles.loadingText, { color: colors.onSurfaceVariant }]}
              allowFontScaling={false}>
              {isSubscriptionChecking ? 'Checking subscription status...' : 'Loading available exams...'}
            </Text>
          </View>
        ) : (
          <>
            {isSingleVendor ? (
              <View style={styles.singleVendorContainer}>
                {renderProducts(filteredCategories[0]?.products || [])}
              </View>
            ) : (
              filteredCategories.map((category) => (
                <List.Accordion
                  key={category.id}
                  title={`${category.name} (${category.products.length})`}
                  expanded={expandedCategoryIds.includes(category.id)}
                  onPress={() => handleAccordionPress(category.id)}
                  style={[styles.accordion, { backgroundColor: colors.surface }]}
                  titleStyle={[styles.accordionTitle, { color: colors.text }]} // Added color
                  left={props => <List.Icon {...props} icon="folder" color={colors.text} />}
                >
                  {renderProducts(category.products)}
                </List.Accordion>
              ))
            )}

            {!isLoading && filteredCategories.length === 0 && (
              <View style={styles.noResultsContainer}>
                <Text style={[styles.noResults, { color: colors.text }]}
                  allowFontScaling={false}>
                  {searchText ? `No exams found matching "${searchText}"` : 'No exams available'}
                </Text>
              </View>
            )}
          </>
        )}
      </ScrollView>

      <BuyNowModal
        buyNowModalVisible={buyNowModalVisible}
        selectedPlan={selectedPlan}
        selectedProduct={selectedProduct}
        setBuyNowModalVisible={setBuyNowModalVisible}
        setSelectedPlan={setSelectedPlan}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 16,
  },
  closeIcon: {
    position: 'absolute',
    right: 5,
    top: 5,
    zIndex: 1,
  },
  searchWrapper: {
    marginTop: 65,
    marginBottom: 16,
  },
  searchWrapperInitial: {
    marginTop: 16,
  },
  searchInput: {
    borderRadius: 28,
    height: 56,
  },
  searchInputText: {
    minHeight: 40,
    fontSize: 16,
  },
  scrollContent: {
    paddingBottom: 32,
  },
  singleVendorContainer: {
    paddingBottom: 16,
  },
  accordion: {
    borderRadius: 12,
    marginBottom: 8,
    overflow: 'hidden',
  },
  accordionTitle: {
    fontWeight: '600',
    fontSize: 16,
  },
  productCard: {
    borderRadius: 12,
    marginVertical: 6,
    marginHorizontal: 8,
    borderWidth: 2,
    overflow: 'hidden',
  },
  productContent: {
    padding: 16,
  },
  productHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  titleContainer: {
    flex: 1,
  },
  titleRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  activeIndicator: {
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 4,
    marginTop: 3,
  },
  activeText: {
    color: '#FFFFFF',
    fontSize: 10,
    fontWeight: 'bold',
  },
  productName: {
    fontWeight: '600',
    fontSize: 16,
    marginBottom: 4,
  },
  metaContainer: {
    flexDirection: 'column',
    marginTop: 4,
  },
  metaItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  metaText: {
    fontSize: 13,
    fontWeight: '500',
  },
  purchaseInfoContainer: {
    marginTop: 8,
  },
  purchaseInfoText: {
    fontSize: 12,
    fontWeight: '500',
    fontStyle: 'italic',
  },
  separator: {
    width: 1,
    height: 12,
    opacity: 0.5,
  },
  actionsContainer: {
    flexDirection: 'row',
    gap: 12,
    marginTop: 16,
  },
  actionButton: {
    flex: 1,
    borderRadius: 8,
    height: 44,
  },
  buttonContent: {
    height: '100%',
  },
  buttonLabel: {
    fontSize: 12,
    fontWeight: '600',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
  },
  noResultsContainer: {
    padding: 20,
    alignItems: 'center',
  },
  noResults: {
    fontSize: 16,
    textAlign: 'center',
    lineHeight: 24,
  },
  purchasedBadge: {
    padding: 10,
    borderRadius: 8,
    width: '100%',
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row',
  },
  purchasedText: {
    color: '#FFFFFF',
    fontWeight: 'bold',
    fontSize: 16,
    textAlign: 'center',
  },
  purchasedIcon: {
    margin: 0,
    padding: 0,
  },
});

export default ProductSelect;