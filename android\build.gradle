buildscript {
    ext {
        buildToolsVersion = "35.0.0"
        minSdkVersion = 24
        compileSdkVersion = 35
        targetSdkVersion = 35
        // ndkVersion = "27.1.12297006"
        ndkVersion = "26.1.10909125"
        kotlinVersion = "2.0.21"
        googlePlayServicesAuthVersion = "20.7.0"
    }
    repositories {
        google()
        mavenCentral()
    }
    dependencies {
        classpath("com.android.tools.build:gradle")
        classpath("com.facebook.react:react-native-gradle-plugin")
        classpath 'com.google.gms:google-services:4.4.2'
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:1.8.0"
    }
}

apply plugin: "com.facebook.react.rootproject"
