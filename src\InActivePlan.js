import {Alert, StyleSheet, View, Pressable} from 'react-native';
import React, {useState} from 'react';
import {Appbar, Text, useTheme} from 'react-native-paper';
import {useProduct} from './store/ProductContext';
import {useNavigation} from '@react-navigation/native';

const InActivePlan = () => {
  const {selectedProduct} = useProduct();
  const navigation = useNavigation();
  const {colors} = useTheme();
  const [modalVisible, setModalVisible] = useState(false);

  return (
    <View style={[styles.container, {backgroundColor: colors.background}]}>
      <View style={{gap: 20}}>
        <Appbar.Header elevated>
          <Appbar.BackAction onPress={() => navigation.goBack()} />
          <Appbar.Content title="InActive Plan" />
        </Appbar.Header>
      </View>

      {/* React Native Paper Modal */}
    </View>
  );
};

export default InActivePlan;

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  centeredContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  link: {
    fontSize: 16,
    fontWeight: '600',
    textDecorationLine: 'underline',
  },
});
