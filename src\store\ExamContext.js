import React, { createContext, useContext, useEffect, useState, useCallback, useMemo } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import apiClient from '../services/ApiClient';

const ExamContext = createContext();

export const ExamProvider = ({ children }) => {
    const [exams, setExams] = useState([]);
    const [selectedExam, setSelectedExam] = useState(null);

    // Memoized API call with deep comparison
    const loadExams = useCallback(async (examCode) => {
        try {
            const data = await apiClient.getExams(examCode ? { exam_code: examCode } : {});
            setExams(prev => {
                // Prevent unnecessary state updates
                return JSON.stringify(prev) === JSON.stringify(data) ? prev : data;
            });
            await AsyncStorage.setItem('exams', JSON.stringify(data));
            /* console.log('Exams loaded:', data); */
        } catch (error) {
            console.error('Failed to load exams:', error);
        }
    }, []);

    // Stable exam selection with storage
    const selectExam = useCallback(async (exam) => {
        setSelectedExam(prev => {
          return JSON.stringify(prev) === JSON.stringify(exam) ? prev : exam;
        });
        await AsyncStorage.setItem('selectedExam', JSON.stringify(exam));
      }, []); 

    // Memoized context value
    const contextValue = useMemo(() => ({
        exams,
        selectedExam,
        loadExams,
        selectExam
    }), [exams, selectedExam, loadExams, selectExam]);
contextValue
    useEffect(() => {
        const init = async () => {
            const [storedExams, storedSelected] = await Promise.all([
                AsyncStorage.getItem('exams'),
                AsyncStorage.getItem('selectedExam')
            ]);
            
            if (storedExams) {
                setExams(prev => {
                    const parsed = JSON.parse(storedExams);
                    return JSON.stringify(prev) === storedExams ? prev : parsed;
                });
            }
            
            if (storedSelected) {
                setSelectedExam(prev => {
                    const parsed = JSON.parse(storedSelected);
                    return JSON.stringify(prev) === storedSelected ? prev : parsed;
                });
            }
        };
        init();
    }, []);

    return (
        <ExamContext.Provider value={contextValue}>
            {children}
        </ExamContext.Provider>
    );
};

export const useExamContext = () => useContext(ExamContext);