import React, { useState, useEffect, useRef, useMemo } from 'react';
import {
  View,
  ScrollView,
  TouchableOpacity,
  StyleSheet,
  Image,
} from 'react-native';
import {
  Text,
  IconButton,
  useTheme,
  Badge,
  Dialog,
  Portal,
  Button,
  Appbar, ActivityIndicator
} from 'react-native-paper';
import { useFocusEffect, useNavigation, useRoute } from '@react-navigation/native';
import AntDesign from 'react-native-vector-icons/AntDesign';
import Entypo from 'react-native-vector-icons/Entypo';
import FontAwesome5 from 'react-native-vector-icons/FontAwesome5';
import Ionicons from 'react-native-vector-icons/Ionicons';
import { useUserProgress } from './store/UserProgressContext';
import { usePreferencesContext } from './store/PreferencesContext';
import { shuffleAndReorderChoices } from './utils/shuffleAndReorderChoices';
import { useExamContext } from './store/ExamContext';

const QnaDetail = () => {
  const [isTransitioning, setIsTransitioning] = useState(false);
  const scrollViewRef = useRef(null);
  const { colors } = useTheme();
  const navigation = useNavigation();
  const route = useRoute();
  const { selectedExam } = useExamContext();

  const {
    initialIndex = 0,
    activeTabLabel = 'All',
    userAnswers = {},
    fromQuizResult = false,
    filteredData = []
  } = route.params;

  const [viewCount, setViewCount] = useState(0); 
  const cachedQuestionsRef = useRef({});

  const [currentIndex, setCurrentIndex] = useState(initialIndex);
  const currentItem = filteredData[currentIndex] || null;
  
  const isBookmarked = Boolean(
    selectedExam?.id &&
    currentItem?.subject &&
    currentItem?.id &&
    progress?.[selectedExam.id]?.[currentItem.subject]?.bookmarked?.some(
      (b) => b.id === currentItem.id
    )
  );
  
  useFocusEffect(
    React.useCallback(() => {
      return () => cachedQuestionsRef.current = {}; 
    }, [])
  );


  const [showAnswer, setShowAnswer] = useState(false);
  const [manualShowAnswer, setManualShowAnswer] = useState(false);
  const [selectedChoice, setSelectedChoice] = useState([]);

  const { shouldShuffleChoices } = usePreferencesContext();

  const { progress, updateProgress, toggleBookmark } = useUserProgress();

  useEffect(() => {
    if (showAnswer && scrollViewRef.current) {
      setTimeout(() => {
        scrollViewRef.current?.scrollToEnd({ animated: true });
      }, 100);
    }
  }, [showAnswer]);

  const handleBookmarkToggle = () => {
    if (!selectedExam?.id || !currentItem?.subject || !currentItem?.id) return;
    toggleBookmark(selectedExam.id, currentItem.subject, currentItem.id);
  };

  useEffect(() => {
    if (selectedExam?.id && currentItem?.subject && currentItem?.id) {
      updateProgress(selectedExam.id, currentItem.subject, currentItem.id);
    }
    /* console.log(progress) */
  }, [currentItem, selectedExam, updateProgress]);

  
  const safeSetCurrentIndex = (newIndex) => {
    if (newIndex >= 0 && newIndex < filteredData.length) {
      setCurrentIndex(newIndex);
      setViewCount(prev => prev + 1); // Step 1: Increment viewCount on navigation
      resetState();
      scrollViewRef.current?.scrollTo({ y: 0, animated: false });
    }
  };

  const handlePrev = () => safeSetCurrentIndex(currentIndex - 1);
  const handleNext = () => safeSetCurrentIndex(currentIndex + 1);


  const resetState = () => {
    setSelectedChoice([]);
    setShowAnswer(false);
    setManualShowAnswer(false);
  };

  useEffect(() => {
    /* console.log('QnaDetail useEffect triggered for selectedChoice', {
      fromQuizResult,
      currentItem: currentItem?.id,
      originalIndex: currentItem?.originalIndex,
      userAnswersKeys: Object.keys(userAnswers)
    }); */

    let userAnswer = [];
    let forceShowAnswer = false;

    if (fromQuizResult) {
      /* console.log('Accessing quiz result answer for index:', currentItem?.originalIndex); */
      userAnswer = (userAnswers[currentItem?.originalIndex] || []).filter(Boolean);
      forceShowAnswer = true;
    } else {
      /* console.log('Using progress answer:', currentItem?.userSelectedAnswer); */
      userAnswer = Array.isArray(currentItem?.userSelectedAnswer)
        ? currentItem.userSelectedAnswer
        : [];
    }

    /* console.log('Final userAnswer for selectedChoice:', userAnswer);
   */
    if (JSON.stringify(selectedChoice) !== JSON.stringify(userAnswer)) {
      setSelectedChoice(userAnswer);
    }
  }, [currentItem, fromQuizResult]); // Removed manualShowAnswer from dependencies

  useEffect(() => {
    /* console.log('QnaDetail useEffect triggered for showAnswer', {
      fromQuizResult,
      currentItem: currentItem?.id,
      manualShowAnswer,
      selectedChoice,
    }); */

    if (!manualShowAnswer) {
      const isMC2 = currentItem?.type === 'mc2';
      const shouldShowAnswer = fromQuizResult || (selectedChoice.length > 0 && !isMC2);
      setShowAnswer(shouldShowAnswer);
    }
  }, [currentItem, fromQuizResult, manualShowAnswer, selectedChoice]);

  const handleShowAnswerToggle = () => {
    setManualShowAnswer(true);
    setShowAnswer(prev => !prev);
  };

  const { shuffledChoices, mappedAnswer, newKeyToOriginalKeyMap } = useMemo(() => {
    if (!currentItem?.choices) {
      return { shuffledChoices: [], mappedAnswer: [], newKeyToOriginalKeyMap: {} };
    }

    const cacheKey = currentItem.id;

    if (fromQuizResult || !shouldShuffleChoices) {
      const cached = cachedQuestionsRef.current[cacheKey];
      if (cached) {
        return cached;
      }
      // Maintain original order and cache
      const processedData = {
        shuffledChoices: currentItem.choices,
        mappedAnswer: currentItem.answer,
        newKeyToOriginalKeyMap: currentItem.choices.reduce((acc, choice) => {
          const key = Object.keys(choice)[0];
          acc[key] = key;
          return acc;
        }, {})
      };
      cachedQuestionsRef.current[cacheKey] = processedData;
      return processedData;
    } else {
      // Shuffle without caching
      return shuffleAndReorderChoices(currentItem.choices, currentItem.answer);
    }
  }, [currentItem, fromQuizResult, shouldShuffleChoices, viewCount]); 


  useEffect(() => {
    const userAnswer = fromQuizResult
      ? (userAnswers[currentItem?.originalIndex] || []).filter(Boolean)
      : Array.isArray(currentItem?.userSelectedAnswer)
        ? currentItem.userSelectedAnswer
        : [];

    setSelectedChoice(userAnswer);
  }, [currentItem, fromQuizResult]);

  // Toggle answer visibility
  const toggleChoice = (key) => {
    if (!showAnswer && !fromQuizResult) {
      const originalKey = newKeyToOriginalKeyMap[key];
      setSelectedChoice(prev =>
        prev.includes(originalKey)
          ? prev.filter(k => k !== originalKey)
          : [...prev, originalKey]
      );
    }
  };

  return (
    <View style={{ flex: 1, backgroundColor: colors.background }}>
      <Appbar.Header elevated>
        <Appbar.BackAction onPress={() => navigation.goBack()} />
        <Appbar.Content title={activeTabLabel} />
        <View style={{
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'space-between',
          paddingHorizontal: 12,
          paddingVertical: 10,
        }}>
          <View style={{ flexDirection: 'row', alignItems: 'center', gap: 16 }}>

            <Text style={{ fontSize: 14, fontWeight: '600', color: colors.onSurface }}>
              {currentIndex + 1} / {filteredData.length}
            </Text>
          </View>
        </View>
      </Appbar.Header>

      <ScrollView ref={scrollViewRef} contentContainerStyle={{ padding: 16 }}>

        {shuffledChoices.length ? (
          <>
            <Text variant="labelSmall" style={{ color: colors.onSurfaceVariant }}>
              {currentItem?.subjectName}
            </Text>

            <Text style={{
              fontSize: 16,
              fontWeight: '600',
              marginTop: 10,
              marginBottom: 20,
              color: colors.onSurface,
            }}>
              {currentItem?.question}
            </Text>
            {showAnswer && currentItem?.ai_explanation && currentItem.ai_explanation?.trim() !== '' && (
              <View style={{
                marginBottom: 10,
                padding: 16,
                backgroundColor: colors.surface,
                borderRadius: 8,
                borderLeftWidth: 4,
                borderLeftColor: colors.primary,
                borderTopLeftRadius: 4,
                borderBottomLeftRadius: 4,
              }}>
                <View style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  gap: 8,
                  marginBottom: 12
                }}>
                  <Ionicons name="information-circle" size={20} color={colors.primary} />
                  <Text variant="titleSmall" style={{
                    color: colors.primary,
                    fontWeight: '700'
                  }}>
                    Explanation
                  </Text>
                </View>
                <Text style={{
                  fontSize: 14,
                  lineHeight: 20,
                  color: colors.onSurfaceVariant,  // Changed from colors.onSurface
                }}>
                  {currentItem?.ai_explanation}
                </Text>
              </View>
            )}
            <View style={{ gap: 10 }} key={currentIndex}>
              {shuffledChoices.map(choiceObj => {
                const [key, value] = Object.entries(choiceObj)[0];
                const isCorrect = mappedAnswer.includes(key);
                const isSelected = selectedChoice.includes(newKeyToOriginalKeyMap[key]);
                const showIncorrect = showAnswer && isSelected && !isCorrect;
                const showCorrect = showAnswer && isCorrect;

                return (
                  <TouchableOpacity
                    key={key}
                    onPress={() => toggleChoice(key)}
                    style={{
                      borderWidth: 2,
                      borderColor: showCorrect ? '#4CAF50' :
                        showIncorrect ? '#f44336' :
                          isSelected ? colors.primary : colors.surfaceVariant,
                      borderRadius: 10,
                      padding: 14,
                      backgroundColor: isSelected ? `${colors.primary}20` : colors.surface,
                      flexDirection: 'row',
                      justifyContent: 'space-between',
                      alignItems: 'center',
                    }}
                  >
                    <Text style={{ fontSize: 16, fontWeight: '500', color: colors.onSurface }}>
                      {key}. {value}
                    </Text>
                    {showCorrect && <AntDesign name="check" size={18} color={'#4CAF50'} />}
                    {showIncorrect && <Entypo name="cross" size={18} color={'#f44336'} />}
                  </TouchableOpacity>
                );
              })}
            </View>
          </>
        ) : (
          <ActivityIndicator size="large" style={{ marginTop: 20 }} />
        )}
      </ScrollView>

      {/* Original Bottom Bar Styling */}
      <View style={{
        flexDirection: 'row',
        justifyContent: 'space-between',
        paddingHorizontal: 12,
        paddingVertical: 8,
        borderTopWidth: 1,
        borderColor: colors.surfaceVariant,
        backgroundColor: colors.background,
      }}>
        <IconButton
          icon="chevron-left"
          disabled={currentIndex === 0}
          onPress={handlePrev}
        />


        <IconButton
          icon={isBookmarked ? 'bookmark' : 'bookmark-outline'}
          onPress={handleBookmarkToggle}
        />

        <IconButton
          icon={() => (
            <Image source={require('../assets/ai.png')} style={{
              height: 30,
              width: 30,
              tintColor: colors.onSurface
            }} />
          )}
          onPress={() => navigation.navigate('ChatScreen')}
        />

        {!fromQuizResult && (
          <IconButton
            icon={() => (
              <Image source={require('../assets/answer.png')} style={{
                height: 22,
                width: 22,
                tintColor: showAnswer ? '#4CAF50' : colors.onSurface
              }} />
            )}
            onPress={handleShowAnswerToggle}
          />
        )}

        <View style={{ flexDirection: 'row', alignItems: 'center', gap: 16 }}>
          <IconButton
            icon="chevron-right"
            disabled={currentIndex === filteredData.length - 1}
            onPress={handleNext}
          />
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  center: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerRightContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 16,
    paddingHorizontal: 12,
  },
  questionText: {
    fontSize: 16,
    fontWeight: '600',
    marginTop: 10,
    marginBottom: 20,
  },
  explanationHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    marginBottom: 12
  },
  bottomBar: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderTopWidth: 1,
  },
  choiceItem: {
    borderWidth: 2,
    borderRadius: 10,
    padding: 14,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  loadingOverlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'rgba(255,255,255,0.7)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1,
  },
});

export default QnaDetail;