import React, {createContext, useState, useMemo, useCallback} from 'react';
import {
  PaperProvider,
  MD3DarkTheme,
  MD3LightTheme,
  MD2DarkTheme,
  MD2LightTheme,
  MD2Theme,
  MD3Theme,
  useTheme,
  adaptNavigationTheme,
  configureFonts,
} from 'react-native-paper';

import {
  InitialState,
  NavigationContainer,
  DarkTheme as NavigationDarkTheme,
  DefaultTheme as NavigationDefaultTheme,
} from '@react-navigation/native';

const {LightTheme, DarkTheme} = adaptNavigationTheme({
  reactNavigationLight: NavigationDefaultTheme,
  reactNavigationDark: NavigationDarkTheme,
});

// Define Light and Dark themes
const CombinedDefaultTheme = {
  ...MD3LightTheme,
  ...LightTheme,
  colors: {
    ...MD3LightTheme.colors,
    ...LightTheme.colors,
  },
};

const CombinedDarkTheme = {
  ...MD3DarkTheme,
  ...DarkTheme,
  colors: {
    ...MD3DarkTheme.colors,
    ...DarkTheme.colors,
  },
};

export const ThemeContext = createContext();

export const ThemeProvider = ({children}) => {
  const [isDarkMode, setIsDarkMode] = useState(false);
  const [isThemeChanging, setIsThemeChanging] = useState(false);

  // Safer theme toggle function
  const toggleTheme = useCallback(() => {
    // Prevent multiple rapid toggles
    if (isThemeChanging) return;

    setIsThemeChanging(true);

    // Use requestAnimationFrame to avoid animation conflicts
    requestAnimationFrame(() => {
      setIsDarkMode(prev => !prev);

      // Reset the changing flag after a short delay
      setTimeout(() => {
        setIsThemeChanging(false);
      }, 300); // Allow animations to complete
    });
  }, [isThemeChanging]);

  // Memoize theme to optimize performance
  const theme = useMemo(
    () => (isDarkMode ? CombinedDarkTheme : CombinedDefaultTheme),
    [isDarkMode],
  );

  return (
    <ThemeContext.Provider value={{
      isDarkMode,
      setIsDarkMode,
      toggleTheme,
      isThemeChanging
    }}>
      <PaperProvider theme={theme}>{children}</PaperProvider>
    </ThemeContext.Provider>
  );
};
