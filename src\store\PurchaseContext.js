import React, { useRef, createContext, useContext, useState, useEffect } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';

import { useLogin } from './LoginContext';

import RevenueCatService from '../services/RevenueCatService';

// Create the context
const PurchaseContext = createContext();

import { GoogleSignin } from '@react-native-google-signin/google-signin';

/**
 * Provider component for purchase state
 */
export const PurchaseProvider = ({ children }) => {
  const [purchases, setPurchases] = useState([]);
  const [userId, setUserId] = useState(null);
  const [subscriptionInfo, setSubscriptionInfo] = useState(null);
  const [availableProducts, setAvailableProducts] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  // Track recent purchase loading operations to prevent duplicates
  const [recentPurchaseLoads, setRecentPurchaseLoads] = useState({});

  // Use LoginContext to get user information
  const loginContext = useLogin();
  const { user, isLoggedIn } = loginContext;

  // TEMPORARILY DISABLED: Load user ID, purchases and subscription status on mount and when login state changes
  /* useEffect(() => {
    const initialize = async () => {
      try {
        console.log('[PurchaseContext] isLoggedIn:', isLoggedIn, 'user:', user?.id);

        // Only set basic state without making API calls
        if (isLoggedIn && user && user.id) {
          console.log('[PurchaseContext] Setting userId without API calls:', user.id);
          setUserId(user.id);
        } else {
          console.log('[PurchaseContext] No user found, clearing state');
          setPurchases([]);
          setSubscriptionInfo(null);
          setUserId(null);
        }

        // COMMENTED OUT API CALLS THAT MIGHT BE CAUSING "NOT ACCESSIBLE" ERROR:
        // await loadPurchases(user.id);
        // await checkSubscriptionStatus();
        // await loadProducts();
      } catch (error) {
        console.error('[PurchaseContext] Error in disabled initialization:', error);
        setPurchases([]);
      }
    };

    initialize();
  }, [isLoggedIn, user]); */

  // Listen for login state changes
  /*  useEffect(() => {
     // When isLoggedIn changes to false (user logs out), clear all purchase data
     if (isLoggedIn === false) {
       console.log('[PurchaseContext] User logged out, clearing purchase data');
       setPurchases([]);
       setSubscriptionInfo(null);
       setUserId(null);
       // Clear the recent purchase loads tracking
       setRecentPurchaseLoads({});
     }
   }, [isLoggedIn]); */
  // since we're directly using the LoginContext which will trigger our main useEffect
  // when the login state changes

  /**
   * TEMPORARILY DISABLED: Load purchases for a specific user from both MongoDB and AsyncStorage
   * @param {string} uid - User ID
   */
  const loadPurchases = async (uid) => {
    try {
      console.log(`[PURCHASEDBG] ====== LOAD PURCHASES TEMPORARILY DISABLED ======`);
      console.log(`[PURCHASEDBG] Would load purchases for user ID: ${uid}`);

      // TEMPORARILY DISABLED - Just set empty purchases to avoid API calls
      setPurchases([]);
      setLoading(false);
      setError(null);

      console.log(`[PURCHASEDBG] ====== LOAD PURCHASES DISABLED - NO API CALLS MADE ======`);
      return;

      // ORIGINAL CODE COMMENTED OUT TO PREVENT "NOT ACCESSIBLE" ERROR:
      /*
      // Check if we've recently loaded purchases for this user (within 2 seconds)
      const now = Date.now();
      const lastLoad = recentPurchaseLoads[uid];
      if (lastLoad && (now - lastLoad < 2000)) {
        console.log(`[PURCHASEDBG] Skipping duplicate purchase load for user ${uid} - loaded ${now - lastLoad}ms ago`);
        return;
      }

      // Update the last load timestamp for this user
      setRecentPurchaseLoads(prev => ({
        ...prev,
        [uid]: now
      }));

      console.log(`[PURCHASEDBG] ====== LOADING PURCHASES ======`);
      console.log(`[PURCHASEDBG] Loading purchases for user ID: ${uid}`);

      setLoading(true);
      setError(null);

      if (!uid) {
        console.log(`[PURCHASEDBG] No user ID provided, cannot load purchases`);
        setPurchases([]);
        return;
      }

      let allPurchases = [];

      // First, try to fetch purchases from MongoDB
      try {
        console.log(`[PURCHASEDBG] Fetching purchases from MongoDB...`);
        const mongoResponse = await apiClient.getUserPurchases('active');
        // ... rest of original code
      } catch (mongoError) {
        // ... error handling
      }
      */
    } catch (error) {
      console.error('[PURCHASEDBG] Error in disabled loadPurchases:', error);
      setPurchases([]);
      setLoading(false);
      setError(null);
    }
  };

  /**
   * Save purchases to AsyncStorage
   * @param {Array} purchasesToSave - Purchases to save
   * @param {string} uid - User ID
   */
  const savePurchases = async (purchasesToSave, uid) => {
    try {
      await AsyncStorage.setItem(`purchases_${uid}`, JSON.stringify(purchasesToSave));
    } catch (error) {
      console.error('Error saving purchases:', error);
      setError('Failed to save purchases');
    }
  };

  // Cache for subscription status to avoid repeated checks
  const subscriptionCache = {};

  // Store subscription status in AsyncStorage

  const storeSubscriptionStatus = async (examCode, isActive) => {
    try {
      if (!isLoggedIn) {
        isActive = false;
        console.log(`[PurchaseContext] set isActive to false because not logged in`);
      }

      const key = `subscription_${examCode}`;
      const lastStatusKey = `lastSubscriptionStatus_${examCode}`;
      const lastTriggeredKey = `lastTriggered_${examCode}`;
      const lastExamCodeKey = `lastExamCode`;

      // Store current status
      await AsyncStorage.setItem(key, JSON.stringify(isActive));
      console.log(`[PurchaseContext] Stored subscription status for ${examCode}: ${isActive}`);
      console.log(`[PurchaseContext] subscriptionInfo?.isActive: ${subscriptionInfo?.isActive}`);

      // Retrieve last status, last triggered time, and last exam code
      const lastStatusJson = await AsyncStorage.getItem(lastStatusKey);
      const lastTriggeredJson = await AsyncStorage.getItem(lastTriggeredKey);
      const lastExamCodeJson = await AsyncStorage.getItem(lastExamCodeKey);
      const lastStatus = lastStatusJson ? JSON.parse(lastStatusJson) : null;
      const lastTriggered = lastTriggeredJson ? JSON.parse(lastTriggeredJson) : 0;
      const lastExamCode = lastExamCodeJson ? JSON.parse(lastExamCodeJson) : null;

      const currentTime = Date.now();
      const oneDay = 24 * 60 * 60 * 1000;

      // Check if status has changed, if a day has passed, or if the exam code has changed
      if (lastStatus !== isActive || currentTime - lastTriggered >= oneDay || lastExamCode !== examCode) {
        console.log(`[storeSubscriptionStatus] Subscription status changed, daily trigger, or exam code changed for ${examCode}:`,
          lastStatus, '→', isActive);

        // Update last status, last triggered time, and last exam code
        await AsyncStorage.setItem(lastStatusKey, JSON.stringify(isActive));
        await AsyncStorage.setItem(lastTriggeredKey, JSON.stringify(currentTime));
        await AsyncStorage.setItem(lastExamCodeKey, JSON.stringify(examCode));

        // Notify QnAContext to reload content
        if (typeof window.subscriptionStatusChanged === 'function') {
          console.log('[storeSubscriptionStatus] window.subscriptionStatusChanged triggered');
          window.subscriptionStatusChanged(examCode, isActive);
        }
      }

    } catch (error) {
      console.error('[PurchaseContext] Error storing subscription status:', error);
    }
  };

  const getSubscriptionsInfo = async (exam_codes, forceRefresh = false, bypassLoginCheck = false) => {
    try {
      // Convert single code to array for consistent processing
      const codesArray = Array.isArray(exam_codes) ? exam_codes : [exam_codes];

      console.log('[getSubscriptionsInfo] userid:', userId);
      console.log('[getSubscriptionsInfo] isLoggedIn:', isLoggedIn);
      console.log('[getSubscriptionsInfo] user:', GoogleSignin.getCurrentUser())
      console.log('[getSubscriptionsInfo] user:', !GoogleSignin.getCurrentUser())
      console.log('exam_codes:', exam_codes);
      if (bypassLoginCheck) {
        console.log('[getSubscriptionsInfo] Bypassing login check');
      } else {
        if (!codesArray.length) {
          return false;
        }
        if (!GoogleSignin.getCurrentUser()) {
          console.log('[getSubscriptionsInfo] user is logged out');
          return false;
        }
      }

      // Get RevenueCat customer info
      const customerInfo = await RevenueCatService.getCustomerInfo(forceRefresh);
      if (!customerInfo) {
        console.log('[getSubscriptionsInfo] No customer info available');
        return Array.isArray(exam_codes) ? {} : false;
      }


      const subscriptions = customerInfo?.subscriptionsByProductIdentifier || {};
      console.log('[getSubscriptionsInfo] Subscriptions:', subscriptions);

      // Create result map
      const resultMap = {};
      const now = new Date().getTime();

      codesArray.forEach(code => {
        if (!code) {
          resultMap[code] = false;
          return;
        }

        // Normalize exam code to match RevenueCat format
        const normalizedCode = String(code).toLowerCase().replace(/-/g, '_');
        //console.log(`[getSubscriptionsInfo] Checking normalized code: ${normalizedCode}`);

        // Find matching subscriptions
        const matchingSubs = Object.values(subscriptions).filter(sub => {
          const subProduct = sub.productIdentifier?.toLowerCase();
          return subProduct && subProduct.includes(normalizedCode);
        });

        // Check if any matching subscription is active and not expired
        const isActive = matchingSubs.some(sub => {
          //const expiresDate = sub.expiresDate ? new Date(sub.expiresDate).getTime() : 0;
          return sub.isActive /* && expiresDate > now */;
        });

        // Include subscription details in result
        resultMap[code] = {
          isActive,
          details: matchingSubs.map(sub => ({
            productId: sub.productIdentifier,
            isActive: sub.isActive,
            purchaseDate: sub.purchaseDate,
            expiresDate: sub.expiresDate,
            willRenew: sub.willRenew
          }))
        };
      });

      console.log('[getSubscriptionsInfo] Final result map:', resultMap);

      if (!Array.isArray(exam_codes)) {
        setSubscriptionInfo(resultMap[exam_codes]);
        storeSubscriptionStatus(exam_codes, resultMap[exam_codes]?.isActive || false);
      }

      return Array.isArray(exam_codes) ? resultMap : resultMap[exam_codes];
    } catch (error) {
      console.error('[RevenueCat] Error checking subscriptions:', error);
      // Return appropriate false values based on input type
      return Array.isArray(exam_codes) ? {} : false;
    }
  };

  /**
   * Check if a specific exam is accessible through subscription or individual purchase
   * @param {string} examId - Exam ID
   * @param {boolean} forceRefresh - Force a fresh check by bypassing any cached results
   * @returns {boolean} - Whether the exam is accessible (via subscription or individual purchase)
   */

  // Context value
  const isSubscriptionActive = async (examCode, forceRefresh = false) => {
    try {
      if (!examCode || !isLoggedIn) {
        console.log('[isSubscriptionActive] No exam code provided');
        return false;
      }

      const subscriptionInfo = await getSubscriptionsInfo(examCode, forceRefresh);
      console.log('[isSubscriptionActive] subscriptionInfo:', subscriptionInfo);

      storeSubscriptionStatus(examCode, subscriptionInfo?.isActive || false);

      return subscriptionInfo?.isActive ?? false;
    } catch (error) {
      console.error('[isSubscriptionActive] Error:', error);
      return false;
    }
  };

  /**
   * Calculate days remaining in subscription
   * @param {string} examId - Exam ID
   * @returns {number} - Days remaining, or 0 if not purchased
   */
  const getRemainingDays = (examId) => {
    // Check if there's a valid global subscription
    if (subscriptionInfo && subscriptionInfo.expiresAt) {
      const now = new Date().getTime();
      const expiry = new Date(subscriptionInfo.expiresAt).getTime();
      if (expiry > now) {
        const diffTime = Math.abs(expiry - now);
        return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
      }
    }

    // If no active subscription, check individual purchases
    if (!purchases || !purchases.length) return 0;
    const purchase = purchases.find(p => p.examId === examId && p.status === 'active');
    if (!purchase) return 0;

    const now = new Date().getTime();
    const expiry = new Date(purchase.expiryDate).getTime();
    if (expiry <= now) return 0;

    const diffTime = Math.abs(expiry - now);
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  };

  /**
   * Add a new purchase
   * @param {string} examId - Exam ID or 'ai_credits' for AI credit purchases
   * @param {number} daysValid - Number of days the purchase is valid for (ignored for AI credits)
   * @param {Object} additionalInfo - Additional purchase information (optional)
   * @returns {Object} - The new purchase object
   */
  const addPurchase = async (examId, daysValid = 30, additionalInfo = {}) => {
    try {
      if (!userId) {
        throw new Error('User not logged in');
      }

      setLoading(true);
      setError(null);

      // Create new purchase object
      const purchaseDate = new Date();

      // Create base purchase object with required MongoDB fields
      let newPurchase = {
        userId,
        examId,
        purchaseDate: purchaseDate.toISOString(),
        status: 'active',
        // MongoDB required fields
        currency: 'USD',
        amount: 5.00, // Default purchase amount
        platform: 'android', // Valid enum value for MongoDB
        transactionId: `purchase_${userId}_${Date.now()}`,
        ...additionalInfo
      };

      // For AI credits, don't set expiry date (they never expire)
      if (examId !== 'ai_credits') {
        const expiryDate = new Date();
        expiryDate.setDate(expiryDate.getDate() + daysValid);
        newPurchase.expiryDate = expiryDate.toISOString();
      }

      // For AI credit purchases, add special properties
      if (examId === 'ai_credits') {
        // Add custom properties to the purchase object
        newPurchase = {
          ...newPurchase,
          purchaseType: 'ai_credits',
          displayName: 'AI Credits Package',
          // Ensure AI credit purchases have correct amount
          amount: 5.00, // AI credits package price
          productId: 'ai_credits_package'
        };
      }

      // Update state and storage
      const updatedPurchases = [...purchases, newPurchase];
      setPurchases(updatedPurchases);
      await savePurchases(updatedPurchases, userId);

      console.log(`[PURCHASEDBG] Purchase added: ${examId}, expires: ${newPurchase.expiryDate || 'never'}`);
      return newPurchase;
    } catch (error) {
      console.error('Error adding purchase:', error);
      setError(error.message || 'Failed to add purchase');
      throw error;
    } finally {
      setLoading(false);
    }
  };

  /**
   * Make a subscription purchase (test mode)
   * @param {Object} product - The product to purchase
   */
  /*  const makePurchase = async (product) => {
     try {
       setLoading(true);
       setError(null);
 
       console.log('[PurchaseContext] Test mode purchase for product:', product);
 
       // Simulate purchase delay
       await new Promise(resolve => setTimeout(resolve, 1000));
 
       // Return mock success result
       const result = {
         success: true,
         transactionId: `test_${Date.now()}`,
         product: product
       };
 
       // Update subscription status after purchase
       await checkSubscriptionStatus();
 
       return result;
     } catch (error) {
       console.error('Purchase error:', error);
       setError(error.message || 'Purchase failed');
       throw error;
     } finally {
       setLoading(false);
     }
   }; */

  /**
   * Restore purchases
   */
  /* const restorePurchases = async () => {
    try {
      setLoading(true);
      setError(null);

      console.log('[PurchaseContext] Restore purchases - test mode only');

      // Update subscription status after restore
      await checkSubscriptionStatus();
      await loadPurchases(userId);
    } catch (error) {
      console.error('Restore purchases error:', error);
      setError(error.message || 'Failed to restore purchases');
      throw error;
    } finally {
      setLoading(false);
    }
  };
 */
  /**
   * Check subscription status
   */
  const checkSubscriptionStatus = async () => {
    try {
      setLoading(true);

      // TEMPORARY: Check for mock subscription in AsyncStorage first
      const mockSubscriptionJson = await AsyncStorage.getItem('subscription_info');
      if (mockSubscriptionJson) {
        try {
          const mockSubscription = JSON.parse(mockSubscriptionJson);
          const now = new Date().getTime();
          const expiryDate = new Date(mockSubscription.expiresAt).getTime();

          // Check if the mock subscription is still valid
          if (expiryDate > now) {
            console.log('Found valid mock subscription:', mockSubscription);
            setSubscriptionInfo(mockSubscription);
            return; // Skip Adapty check if we have a valid mock subscription
          } else {
            console.log('Mock subscription expired, removing');
            await AsyncStorage.removeItem('subscription_info');
          }
        } catch (parseError) {
          console.error('Error parsing mock subscription:', parseError);
          await AsyncStorage.removeItem('subscription_info');
        }
      }

      // No Adapty integration - only use mock subscriptions
      setSubscriptionInfo(null);
    } catch (error) {
      console.error('Error checking subscription status:', error);
      setError('Failed to check subscription status');
    } finally {
      setLoading(false);
    }
  };

  /**
   * Load available products (test mode)
   */
  const loadProducts = async () => {
    try {
      setLoading(true);
      console.log('[PurchaseContext] Load products - test mode only');
      // Set empty products array for test mode
      setAvailableProducts([]);
    } catch (error) {
      console.error('Error loading products:', error);
      setError('Failed to load products');
    } finally {
      setLoading(false);
    }
  };

  /**
   * Get all purchases for the current user
   * @returns {Array} - Array of purchases
   */
  const getPurchases = () => {
    return purchases;
  };

  /**
   * Refresh purchases and subscription status
   */
  const refreshPurchases = async () => {
    try {
      // If we have a userId, check if we've recently loaded purchases for this user
      if (userId) {
        const now = Date.now();
        const lastLoad = recentPurchaseLoads[userId];
        if (lastLoad && (now - lastLoad < 2000)) {
          console.log(`[PURCHASEDBG] Skipping duplicate purchase refresh for user ${userId} - loaded ${now - lastLoad}ms ago`);
          return;
        }

        // Update the last load timestamp for this user
        setRecentPurchaseLoads(prev => ({
          ...prev,
          [userId]: now
        }));
      }

      console.log(`[PURCHASEDBG] Refreshing purchases for user: ${userId || 'not logged in'}`);
      setLoading(true);

      // Clear subscription cache when refreshing purchases
      Object.keys(subscriptionCache).forEach(key => {
        delete subscriptionCache[key];
      });

      // If user is not logged in, check AsyncStorage before clearing purchase data
      if (!isLoggedIn || !userId) {
        try {
          // Check if there's a user in AsyncStorage
          const userJson = await AsyncStorage.getItem('user');
          if (userJson) {
            const storedUser = JSON.parse(userJson);
            if (storedUser && storedUser.id) {
              // Update userId in state
              setUserId(storedUser.id);

              // Load purchases for this user
              await loadPurchases(storedUser.id);
              return;
            }
          }
        } catch (error) {
          console.error('[PURCHASEDBG] Error checking AsyncStorage for user:', error);
        }

        // If we get here, there's no user in AsyncStorage either
        setPurchases([]);
        setSubscriptionInfo(null);
        return;
      }

      // First check subscription status with Adapty
      await checkSubscriptionStatus();

      // Then load purchases from AsyncStorage
      await loadPurchases(userId);

      // Also load available products
      await loadProducts();

      console.log(`[PURCHASEDBG] Purchases refreshed: ${purchases.length} items`);
    } catch (error) {
      console.error('[PURCHASEDBG] Error refreshing purchases:', error);
      setError('Failed to refresh purchases');
    } finally {
      setLoading(false);
    }
  };

  /**
   * Clear purchases when user logs out
   */
  const clearPurchases = async () => {
    console.log(`[PURCHASEDBG] Clearing purchases for user: ${userId || 'not set'}`);

    // Clear all purchase-related state
    setPurchases([]);
    setUserId(null);
    setSubscriptionInfo(null);

    // Clear subscription cache
    Object.keys(subscriptionCache).forEach(key => {
      delete subscriptionCache[key];
    });

    // Clear the recent purchase loads tracking
    setRecentPurchaseLoads({});

    // If we have a userId, also clear AsyncStorage
    if (userId) {
      try {
        const storageKey = `purchases_${userId}`;
        console.log(`[PURCHASEDBG] Removing purchases from AsyncStorage with key: ${storageKey}`);
        await AsyncStorage.removeItem(storageKey);
      } catch (error) {
        console.error('[PURCHASEDBG] Error clearing AsyncStorage purchases:', error);
      }
    }

    console.log(`[PURCHASEDBG] Purchases and cache cleared`);
  };

  /**
   * Update user ID and load purchases when user changes
   * @param {string} newUserId - New user ID
   * @param {Object} [options] - Additional options
   * @param {Object} [options.revenueCatCustomerInfo] - RevenueCat customer info to store
   */
  const updateUser = async (newUserId, options = {}) => {
    console.log(`[PURCHASEDBG] Updating user: ${userId || 'not set'} → ${newUserId || 'null'}`);
    console.log(`[PURCHASEDBG] Update options:`, options);

    if (!newUserId && !options.revenueCatCustomerInfo) {
      return;
    }

    // If RevenueCat customer info is provided, update subscription info
    if (options.revenueCatCustomerInfo) {
      console.log('[PURCHASEDBG] Updating with RevenueCat customer info');
      const customerInfo = options.revenueCatCustomerInfo;
      const activeSubscriptions = customerInfo.activeSubscriptions || [];

      console.log('[PURCHASEDBG] RevenueCat active subscriptions:', activeSubscriptions);

      // Create subscription info object
      const newSubscriptionInfo = {
        ...subscriptionInfo,
        revenueCatCustomerInfo: customerInfo,
        expiresAt: customerInfo.latestExpirationDate || subscriptionInfo?.expiresAt,
        isActive: customerInfo.activeSubscriptions?.length > 0
      };

      setSubscriptionInfo(newSubscriptionInfo);

      // Notify all components about subscription change
      if (typeof window.subscriptionStatusChanged === 'function') {
        window.subscriptionStatusChanged(null, customerInfo.activeSubscriptions?.length > 0);
      }
    }

    // Check if we've recently loaded purchases for this user (within 2 seconds)
    const now = Date.now();
    const lastLoad = recentPurchaseLoads[newUserId];
    if (lastLoad && (now - lastLoad < 2000) && newUserId === userId) {
      console.log(`[PURCHASEDBG] Skipping duplicate user update for ${newUserId} - updated ${now - lastLoad}ms ago`);
      return;
    }

    // Update the last load timestamp for this user
    setRecentPurchaseLoads(prev => ({
      ...prev,
      [newUserId]: now
    }));

    // Clear subscription cache when user changes
    Object.keys(subscriptionCache).forEach(key => {
      delete subscriptionCache[key];
    });

    if (newUserId === userId) {
      // User ID unchanged, just refresh purchases
      await refreshPurchases();
      return;
    }

    // Update the user ID in state - CRITICAL: This must happen immediately
    console.log(`[PURCHASEDBG] BEFORE setUserId - current userId: ${userId}, setting to: ${newUserId}`);

    // Force immediate state update using functional update
    setUserId(prevUserId => {
      console.log(`[PURCHASEDBG] FUNCTIONAL UPDATE: ${prevUserId} → ${newUserId}`);
      return newUserId;
    });

    // Force a delay to ensure state update is processed
    await new Promise(resolve => setTimeout(resolve, 200));

    console.log(`[PURCHASEDBG] AFTER setUserId - userId should now be: ${newUserId}, isLoggedIn: ${isLoggedIn}`);

    if (newUserId) {
      // Load purchases from AsyncStorage
      await loadPurchases(newUserId);

      // Check subscription status
      await checkSubscriptionStatus();

      console.log(`[PURCHASEDBG] User updated: ${purchases.length} purchases loaded`);

      // Verify userId is actually set after all operations
      console.log(`[PURCHASEDBG] Final verification - userId in state: ${userId}`);
      if (!userId) {
        console.warn(`[PURCHASEDBG] WARNING: userId still null after updateUser, forcing multiple state updates`);

        // Force multiple state updates with different approaches
        setUserId(newUserId);

        // Use setTimeout to force another update in the next tick
        setTimeout(() => {
          setUserId(prevId => {
            console.log(`[PURCHASEDBG] TIMEOUT UPDATE: ${prevId} → ${newUserId}`);
            return newUserId;
          });
        }, 50);

        // Force another update after a longer delay
        setTimeout(() => {
          setUserId(newUserId);
          console.log(`[PURCHASEDBG] FINAL FORCE UPDATE: userId set to ${newUserId}`);
        }, 100);
      }

      // Also initialize/restart the sync service with the correct userId
      try {
        console.log(`[PURCHASEDBG] Initializing sync service with userId: ${newUserId}`);
        // const RealTimeSyncService = require('../services/RealTimeSyncService').default;
        //RealTimeSyncService.setAppActive(true);
        //RealTimeSyncService.initialize(newUserId);

        // Initialize user progress sync service
        const userProgressSyncService = require('../services/UserProgressSyncService').default;
        userProgressSyncService.initializeForUser(newUserId);

        console.log(`[PURCHASEDBG] Sync services initialized successfully`);
      } catch (syncError) {
        console.error(`[PURCHASEDBG] Error initializing sync services:`, syncError);
      }
    } else {
      clearPurchases();
    }
  };

  /**
   * Log all purchases for debugging purposes
   */
  const logAllPurchases = () => {
    console.log(`[PURCHASEDBG] ====== LOGGING ALL PURCHASES ======`);
    console.log(`[PURCHASEDBG] Current user ID: ${userId || 'not set'}`);
    console.log(`[PURCHASEDBG] Number of purchases: ${purchases.length}`);

    if (purchases.length === 0) {
      console.log(`[PURCHASEDBG] No purchases found`);
    } else {
      purchases.forEach((purchase, index) => {
        console.log(`[PURCHASEDBG] Purchase ${index + 1}:`);
        console.log(`[PURCHASEDBG]   - examId: ${purchase.examId} (${typeof purchase.examId})`);
        console.log(`[PURCHASEDBG]   - userId: ${purchase.userId}`);
        console.log(`[PURCHASEDBG]   - status: ${purchase.status}`);
        if (purchase.expiryDate) {
          const now = new Date().getTime();
          const expiry = new Date(purchase.expiryDate).getTime();
          console.log(`[PURCHASEDBG]   - expiryDate: ${purchase.expiryDate}`);
          console.log(`[PURCHASEDBG]   - is expired: ${expiry <= now}`);
        } else {
          console.log(`[PURCHASEDBG]   - no expiry date (never expires)`);
        }
        console.log(`[PURCHASEDBG]   - purchaseDate: ${purchase.purchaseDate || 'not set'}`);
        if (purchase.isTestPurchase) {
          console.log(`[PURCHASEDBG]   - isTestPurchase: true`);
        }
      });
    }

    console.log(`[PURCHASEDBG] ====== PURCHASE LOG COMPLETE ======`);
  };

  /**
   * Add a test purchase for debugging purposes
   * @param {string} examId - Exam ID to add a test purchase for
   */
  const addTestPurchase = async (examId) => {
    try {
      console.log(`[PURCHASEDBG] ====== ADDING TEST PURCHASE ======`);
      console.log(`[PURCHASEDBG] Adding test purchase for exam: ${examId}`);
      console.log(`[PURCHASEDBG] Current user ID: ${userId || 'not set'}`);

      if (!userId) {
        console.log(`[PURCHASEDBG] No user ID set, cannot add test purchase`);
        return false;
      }

      if (!examId) {
        console.log(`[PURCHASEDBG] No exam ID provided, cannot add test purchase`);
        return false;
      }

      // Create a new purchase with 365 days validity
      const purchaseDate = new Date();
      const expiryDate = new Date();
      expiryDate.setDate(expiryDate.getDate() + 365);

      const testPurchase = {
        userId,
        examId,
        purchaseDate: purchaseDate.toISOString(),
        expiryDate: expiryDate.toISOString(),
        status: 'active',
        isTestPurchase: true,
        // MongoDB required fields
        currency: 'USD',
        amount: 5.00, // Test purchase amount
        platform: 'android', // Valid enum value for MongoDB
        transactionId: `test_purchase_${userId}_${Date.now()}`,
        productId: examId
      };

      console.log(`[PURCHASEDBG] Created test purchase:`, testPurchase);

      // Update state and storage
      const updatedPurchases = [...purchases, testPurchase];
      setPurchases(updatedPurchases);

      // Save purchases to AsyncStorage
      const purchasesJson = JSON.stringify(updatedPurchases);
      await AsyncStorage.setItem(`purchases_${userId}`, purchasesJson);
      console.log(`[PURCHASEDBG] Saved ${updatedPurchases.length} purchases to AsyncStorage`);

      console.log(`[PURCHASEDBG] Test purchase added and saved to AsyncStorage`);
      console.log(`[PURCHASEDBG] ====== TEST PURCHASE ADDED ======`);

      return true;
    } catch (error) {
      console.error(`[PURCHASEDBG] Error adding test purchase:`, error);
      return false;
    }
  };

  /**
   * Clear the purchase load tracking to force a fresh load
   */
  const clearPurchaseLoadTracking = () => {
    console.log('[PURCHASEDBG] Clearing purchase load tracking');
    setRecentPurchaseLoads({});
  };

  const value = {
    purchases,
    subscriptionInfo,
    availableProducts,
    loading,
    error,
    storeSubscriptionStatus,
    isSubscriptionActive,
    getSubscriptionsInfo,
    getRemainingDays,
    addPurchase,
    /* makePurchase, */ // handled by revenucat
    /* restorePurchases, */ //handled by revenucat
    refreshPurchases,
    clearPurchases,
    updateUser,
    getPurchases,
    addTestPurchase, // Add the test function to the context
    logAllPurchases, // Add the log function to the context
    clearPurchaseLoadTracking, // Add the tracking clear function
    isLoggedIn,
    userId,
    user,
    // Add subscription status for easy access
    subscriptionActive: isLoggedIn ? (subscriptionInfo?.isActive || false) : false
  };

  return (
    <PurchaseContext.Provider value={value}>
      {children}
    </PurchaseContext.Provider>
  );
};

/**
 * Hook to use the purchase context
 */
export const usePurchase = () => {
  const context = useContext(PurchaseContext);
  if (context === undefined) {
    throw new Error('usePurchase must be used within a PurchaseProvider');
  }
  return context;
};

export default PurchaseContext;