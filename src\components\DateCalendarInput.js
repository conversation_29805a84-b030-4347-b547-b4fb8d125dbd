import React from 'react';
import { TouchableOpacity, View, StyleSheet } from 'react-native';
import { Text, useTheme, Portal, Modal } from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import CalendarPicker from 'react-native-calendar-picker';

const DateCalendarInput = ({ startDate, endDate, onDateChange }) => {
  const { colors } = useTheme();
  const [showCalendar, setShowCalendar] = React.useState(false);

  const formatDate = (date) => date ? date.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
  }) : '';

  const handleResetDates = () => {
    onDateChange(null, 'START_DATE');
    onDateChange(null, 'END_DATE');
  };

  return (
    <>
      <View style={styles.sectionHeader}>
        <Text variant="titleSmall" style={[styles.sectionTitle, { color: colors.onSurface }]}>
          DATE RANGE
        </Text>
        <TouchableOpacity onPress={handleResetDates}>
          <Text variant="labelMedium" style={{ color: colors.primary }}>
            Reset
          </Text>
        </TouchableOpacity>
      </View>

      <TouchableOpacity
        style={[
          styles.dateButton,
          {
            backgroundColor: colors.surface, // Changed from surfaceVariant to surface
            borderColor: colors.outlineVariant, // Added border for consistency
            borderWidth: 0
          }
        ]}
        onPress={() => setShowCalendar(true)}
      >
        <Icon name="calendar-month" size={18} color={colors.primary} />
        <View style={styles.dateTextContainer}>
          <Text variant="bodyMedium" style={{ color: colors.onSurface }}>
            {startDate ? (
              endDate ? (
                startDate.getTime() === endDate.getTime() ?
                  formatDate(startDate)
                  : `${formatDate(startDate)}  –  ${formatDate(endDate)}`
              ) : formatDate(startDate)
            ) : 'All Dates'}
          </Text>
          {/* <Text variant="labelSmall" style={{ color: colors.onSurfaceVariant }}>
            {startDate ?
              (endDate ?
                (startDate.getTime() === endDate.getTime() ? 'Date selected' : 'Dates selected')
                : 'Date selected')
              : 'Tap to select date or range'}
          </Text> */}
        </View>
        <Icon name="chevron-right" size={18} color={colors.onSurfaceVariant} />
      </TouchableOpacity>

      <Portal>
        <Modal
          visible={showCalendar}
          onDismiss={() => setShowCalendar(false)}
          contentContainerStyle={[
            styles.calendarModal,
            { backgroundColor: colors.surface }
          ]}
        >
          <View style={styles.calendarModalHeader}>
            <Text variant="titleMedium" style={{ color: colors.onSurface }}>
              Select Date or Range
            </Text>
            <TouchableOpacity
              onPress={() => setShowCalendar(false)}
              style={styles.doneButton}
            >
              <Text variant="labelLarge" style={{ color: colors.primary }}>
                Done
              </Text>
            </TouchableOpacity>
          </View>

          <CalendarPicker
            allowRangeSelection
            allowBackwardRangeSelect
            onDateChange={(date, type) => {
              onDateChange(date, type);
              // Remove the automatic close on END_DATE selection
            }}
            selectedDayColor={colors.primary}
            selectedDayTextColor={colors.onPrimary}
            todayBackgroundColor={colors.surfaceVariant}
            todayTextStyle={{ color: colors.onSurfaceVariant }}
            textStyle={{ color: colors.onSurface }}
            previousComponent={<Icon name="arrow-left" size={20} color={colors.primary} />}
            nextComponent={<Icon name="arrow-right" size={20} color={colors.primary} />}
            selectedStartDate={startDate}
            selectedEndDate={endDate}
            width={340}
          />
        </Modal>
      </Portal>
    </>
  );
};

const styles = StyleSheet.create({
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  sectionTitle: {
    letterSpacing: 0.5,
  },
  dateButton: {
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: 12,
    padding: 16,
    gap: 12,
  },
  dateTextContainer: {
    flex: 1,
    gap: 2,
  },
  calendarModal: {
    paddingVertical: 20,
    margin: 20,
    borderRadius: 16,
    flexDirection: 'column',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  calendarModalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
    width: 305,
  },
});

export default DateCalendarInput;