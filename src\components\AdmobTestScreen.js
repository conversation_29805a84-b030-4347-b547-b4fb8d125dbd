import React, { useState, useEffect } from 'react';
import { View, ScrollView, StyleSheet, Alert, LogBox } from 'react-native';
import { useTheme, <PERSON>ton, Card, Text, Divider } from 'react-native-paper';
import { BannerAdSize } from 'react-native-google-mobile-ads';
import AdmobService from '../services/AdmobService';

// Ignore specific warnings
LogBox.ignoreLogs([
  'NativeAdView: .MediaView must be a direct child',
  'NativeAdView: .HeadlineView must be a direct child',
]);

const AdmobTestScreen = () => {
  const theme = useTheme();
  const [isLoading, setIsLoading] = useState(false);
  const [reward, setReward] = useState(null);

  const loadInterstitial = () => {
    setIsLoading(true);
    AdmobService.showInterstitialAd(
      () => setIsLoading(false),
      () => {
        Alert.alert('Info', 'Interstitial ad closed');
        setIsLoading(false);
      }
    );
  };

  const loadRewarded = () => {
    setIsLoading(true);
    AdmobService.showRewardedAd(
      (reward) => {
        setReward(reward);
        Alert.alert('Reward', `Earned ${reward.amount} ${reward.type}`);
      },
      () => {
        setIsLoading(false);
      }
    );
  };

  return (
    <ScrollView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <Card style={styles.card}>
        <Card.Title title="AdMob Test Screen" />
        <Card.Content>
          <Text style={{ color: theme.colors.onSurface }}>
            Test different ad formats from react-native-google-mobile-ads
          </Text>
        </Card.Content>
      </Card>

      {/* Banner Ads */}
      <Card style={styles.card}>
        <Card.Title title="Banner Ads" />
        <Card.Content>
          <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant }}>
            Adaptive Banner (320x50)
          </Text>
          <View style={styles.adContainer}>
            {AdmobService.renderBannerAd(
              BannerAdSize.BANNER,
              (error) => console.log('Banner ad failed to load:', error)
            )}
          </View>

          <Divider style={styles.divider} />

          <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant }}>
            Medium Rectangle (300x250)
          </Text>
          <View style={styles.adContainer}>
            {AdmobService.renderBannerAd(
              BannerAdSize.MEDIUM_RECTANGLE,
              (error) => console.log('Medium Rectangle ad failed to load:', error)
            )}
          </View>
        </Card.Content>
      </Card>

      {/* Interstitial Ad */}
      <Card style={styles.card}>
        <Card.Title title="Interstitial Ad" />
        <Card.Content>
          <Button
            mode="contained"
            onPress={loadInterstitial}
            disabled={isLoading}
            loading={isLoading}
          >
            Load & Show Interstitial
          </Button>
        </Card.Content>
      </Card>

      {/* Rewarded Ad */}
      <Card style={styles.card}>
        <Card.Title title="Rewarded Ad" />
        <Card.Content>
          <Button
            mode="contained"
            onPress={loadRewarded}
            disabled={isLoading}
            loading={isLoading}
          >
            Load & Show Rewarded Ad
          </Button>
        </Card.Content>
      </Card>

      {/* Native Ad - Temporarily disabled due to API changes */}
      <Card style={styles.card}>
        <Card.Title title="Native Ad" />
        <Card.Content>
          <Text style={{ color: theme.colors.onSurfaceVariant, textAlign: 'center', padding: 20 }}>
            Native Ad implementation requires using the NativeAd.createForAdRequest() API.
            This will be implemented in a future update.
          </Text>
        </Card.Content>
      </Card>

      {/* Loading Indicator */}
      {isLoading && (
        <Card style={styles.card}>
          <Card.Content style={styles.loadingContainer}>
            <Text style={{ color: theme.colors.onSurface }}>
              Loading ad...
            </Text>
          </Card.Content>
        </Card>
      )}
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  card: {
    marginBottom: 16,
  },
  adContainer: {
    alignItems: 'center',
    marginVertical: 8,
    minHeight: 100,
    justifyContent: 'center',
  },
  divider: {
    marginVertical: 12,
  },
  nativeAd: {
    width: '100%',
    alignItems: 'center',
  },
  nativeAdHeadline: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  nativeAdMedia: {
    width: 300,
    height: 200,
    marginVertical: 8,
  },
  nativeAdBody: {
    marginVertical: 8,
  },
  nativeAdCTA: {
    backgroundColor: '#4285F4',
    padding: 10,
    borderRadius: 4,
    marginTop: 8,
  },
  loadingContainer: {
    alignItems: 'center',
    padding: 16,
  },
});

export default AdmobTestScreen;