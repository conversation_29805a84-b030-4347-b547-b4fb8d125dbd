import React, { createContext, useContext, useState, useEffect, useMemo } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { ExamProvider } from './ExamContext';
import { QnAProvider } from './QnAContext';
import { UserProgressProvider } from './UserProgressContext';
import { useExamContext } from './ExamContext';
import { PurchaseProvider } from './PurchaseContext';
import { AICreditProvider } from './AICreditContext';
import { AIChatProvider } from './AIChatContext';
import { LoginProvider } from './LoginContext';

const AppContext = createContext();

const ContextSyncer = () => {
  const { selectedOption } = useAppContext();
  const { selectExam } = useExamContext();

  // Memoize the exam data based on selectedOption's properties
  const examData = useMemo(() => {
    if (!selectedOption) return null;
    return {
      id: selectedOption.id,
      exam_code: selectedOption.exam_code,
      subjects: selectedOption.subjects,
      exam_name: selectedOption.exam_name,
      total_questions: selectedOption.total_questions,
    };
  }, [selectedOption?.id, selectedOption?.exam_code, selectedOption?.subjects, selectedOption?.total_questions]);

  useEffect(() => {
    const updateExamSelection = async () => {
      if (examData) {
        await selectExam(examData);
      }
    };

    updateExamSelection();
  }, [examData, selectExam]); // Depend on memoized examData and stable selectExam

  return null;
};


export const AppProvider = ({ children }) => {
  const [selectedOption, setSelectedOption] = useState(null);

  useEffect(() => {
    const loadSelectedProduct = async () => {
      try {
        const storedProduct = await AsyncStorage.getItem('selectedProduct');
        if (storedProduct) {
          const parsedProduct = JSON.parse(storedProduct);
          setSelectedOption(prev => {
            // Deep compare to prevent unnecessary updates
            return JSON.stringify(prev) === JSON.stringify(parsedProduct)
              ? prev
              : parsedProduct;
          });
        }
      } catch (error) {
        console.error('Error loading selected product:', error);
      }
    };

    loadSelectedProduct();
  }, []);

  return (
    <AppContext.Provider value={{ selectedOption, setSelectedOption }}>
      <LoginProvider>
        <ExamProvider>
          <PurchaseProvider>
            <AICreditProvider>
              <AIChatProvider>
                <UserProgressProvider>
                  <QnAProvider>
                    <ContextSyncer />
                    {children}
                  </QnAProvider>
                </UserProgressProvider>
              </AIChatProvider>
            </AICreditProvider>
          </PurchaseProvider>
        </ExamProvider>
      </LoginProvider>
    </AppContext.Provider>
  );
};

export const useAppContext = () => useContext(AppContext);
