export const accordionData = [
  {
    title: 'Smartphones',
    category: 'Electronics',
    brands: ['Apple', 'Samsung', 'Realme', 'OnePlus', 'Xiaomi'],
    subtitle: 'Latest Gadgets',
    description: 'Find the best smartphones from top brands.',
    weeklyRate: '$10',
    monthlyRate: '$35',
  },
  {
    title: 'Laptops',
    category: 'Electronics',
    brands: ['Apple', 'Dell', 'HP', 'Asus', 'Lenovo'],
    subtitle: 'High Performance Computing',
    description: 'Powerful laptops for work, gaming, and entertainment.',
    weeklyRate: '$15',
    monthlyRate: '$50',
  },
  {
    title: 'Headphones',
    category: 'Audio & Music',
    brands: ['Sony', 'Bose', 'JBL', 'Sennheiser', 'Beats'],
    subtitle: 'Immersive Sound',
    description: 'Best noise-canceling and high-quality audio devices.',
    weeklyRate: '$8',
    monthlyRate: '$25',
  },
  {
    title: 'Gaming',
    category: 'Entertainment',
    brands: ['PlayStation', 'Xbox', 'Nintendo', 'Alienware', 'Razer'],
    subtitle: 'Ultimate Gaming Experience',
    description: 'Consoles, gaming PCs, and accessories for gamers.',
    weeklyRate: '$12',
    monthlyRate: '$40',
  },
  {
    title: 'Cameras',
    category: 'Photography',
    brands: ['Canon', 'Nikon', 'Sony', 'Fujifilm', 'Panasonic'],
    subtitle: 'Capture Every Moment',
    description: 'High-quality cameras for photography and vlogging.',
    weeklyRate: '$20',
    monthlyRate: '$70',
  },
];
