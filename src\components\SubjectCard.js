import React from 'react';
import { View, StyleSheet } from 'react-native';
import { Card, ProgressBar, useTheme, Text } from 'react-native-paper';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';

export const SubjectCard = React.memo(({ 
  subject, 
  metricTitle, 
  metricValue, 
  onPress 
}) => {
  const { colors } = useTheme();

  return (
    <Card style={[styles.subjectCard, { backgroundColor: colors.surface }]} onPress={onPress}>
      <Card.Content>
        <View style={styles.cardHeader}>
          <MaterialCommunityIcons
            name="book-open-outline"
            size={20}
            color={colors.primary}
            style={styles.bookIcon}
          />
          <Text
            variant="titleMedium"
            style={[styles.subjectTitle, { color: colors.onSurface }]}
            numberOfLines={2} maxFontSizeMultiplier={1.5}
          >
            {subject.name}
          </Text>
        </View>

        <View style={styles.progressContainer}>
          <View style={styles.statsRow}>
            <Text style={[styles.progressText, { color: colors.primary }]} maxFontSizeMultiplier={1.5}>
              {subject.percentage}
            </Text>
            <Text style={[styles.metaText, { color: colors.onSurfaceVariant }]} maxFontSizeMultiplier={1.5}>
              {metricValue}/{subject.total} {metricTitle}
            </Text>
          </View>
          <ProgressBar
            progress={subject.progress}
            color={colors.primary}
            style={[styles.progressBar, { backgroundColor: `${colors.primary}20` }]}
          />
        </View>
      </Card.Content>
      <MaterialCommunityIcons
        name="chevron-right"
        size={24}
        color={colors.onSurfaceVariant}
        style={styles.chevron}
      />
    </Card>
  );
});

const styles = StyleSheet.create({
  subjectCard: {
    borderRadius: 12,
    padding: 8,
    position: 'relative',
    marginBottom: 6,
  },
  cardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  bookIcon: {
    marginRight: 12,
  },
  subjectTitle: {
    flex: 1,
    fontWeight: '600',
    lineHeight: 22,
  },
  progressContainer: {
    marginLeft: 32,
  },
  statsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  progressText: {
    fontWeight: '700',
    fontSize: 15,
  },
  metaText: {
    fontSize: 13,
  },
  progressBar: {
    height: 6,
    borderRadius: 3,
  },
  chevron: {
    position: 'absolute',
    right: 16,
    top: '50%',
    marginTop: -12,
  }
});

export default SubjectCard;