import { View, StyleSheet, TouchableOpacity } from 'react-native';
import React, { useState } from 'react';
import {
  Avatar, useTheme, Text,
  Appbar,
  Badge,
} from 'react-native-paper';
import { useNavigation } from '@react-navigation/native';
import FontAwesome5 from 'react-native-vector-icons/FontAwesome5';
import CustomAppbarContent from '../components/CustomAppbarContent';

const ChatHeader = ({ onCreditPress }) => {  
  const theme = useTheme(); // <-- Get the entire theme object
  const navigation = useNavigation();

  return (
    <Appbar.Header elevated style={{ backgroundColor: theme.colors.gray }}>
      <Appbar.BackAction onPress={() => navigation.goBack()} />

      <CustomAppbarContent title="Ask AI" />
      <View>
        <TouchableOpacity
          onPress={onCreditPress} 
          style={{
            position: 'absolute',
            top: -10,
            right: 20,
            zIndex: 1,
            flexDirection: 'row',
            alignItems: 'center'
          }}
        >
          <FontAwesome5 name="coins" size={18} color={theme.colors.primary} />
          <Badge
            style={{
              position: 'absolute',
              top: -6,
              right: -10,
              backgroundColor: theme.colors.primary,
              color: theme.colors.onPrimary
            }}
          >
            10
          </Badge>
        </TouchableOpacity>
        </View>
    </Appbar.Header>
  );
};


const styles = StyleSheet.create({
  /* titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginLeft: 16,
  }, */
  avatar: {
    marginRight: 8,
    backgroundColor: 'transparent',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginLeft: 8,
  },
  headerRight: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 16
  },
  creditButton: {
    marginLeft: 16,
    padding: 8,
    position: 'relative'
  },
  badge: {
    position: 'absolute',
    top: -6,
    right: -6
  }
});

export default ChatHeader;