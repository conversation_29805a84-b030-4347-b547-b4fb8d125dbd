import React from 'react';
import { View, StyleSheet, TouchableOpacity } from 'react-native';
import { Text, Badge, useTheme } from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { useAICredit } from '../store/AICreditContext';

/**
 * A component that displays the user's AI credit balance
 * and allows them to tap to open the credit modal
 */
const AICreditBadge = ({ onPress }) => {
  const { colors } = useTheme();
  const { credits } = useAICredit();
  
  return (
    <TouchableOpacity 
      style={styles.container}
      onPress={onPress}
      activeOpacity={0.7}
    >
      <View style={[
        styles.badgeContainer, 
        { 
          backgroundColor: `${colors.primary}20`,
          borderWidth: 2,
          borderColor: colors.primary,
          borderRadius: 10,
        }
      ]}>
        <Icon name="brain" size={18} color={colors.primary} style={styles.icon} />
        <Text style={[styles.creditText, { color: colors.onSurface }]} maxFontSizeMultiplier={1.2}>
          {credits}
        </Text>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    marginRight: 8,
  },
  badgeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 10,
    paddingVertical: 4,
  },
  icon: {
    marginRight: 4,
  },
  creditText: {
    fontSize: 14,
    fontWeight: 'bold',
  },
});

export default AICreditBadge;
