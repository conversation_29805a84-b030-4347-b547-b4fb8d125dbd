import React, { useRef, useState } from 'react';
import { View, StyleSheet } from 'react-native';
import { useTheme, TextInput as PaperTextInput, IconButton, Text } from 'react-native-paper';

const MAX_CHAR_LIMIT = 80;

const styles = StyleSheet.create({
  container: {
    flexDirection: 'column',
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  outlineStyle: {
    borderRadius: 20,
    borderWidth: 1,
  },
  input: {
    flex: 1,
    height: 40,
  },
  warningText: {
    fontSize: 12,
    marginTop: 4,
    marginLeft: 4,
  },
  charCount: {
    fontSize: 10,
    position: 'absolute',
    bottom: 8,
    right: 56, // Position to the left of the send button
    zIndex: 1,
    backgroundColor: 'rgba(255,255,255,0.7)',
    paddingHorizontal: 4,
    borderRadius: 4,
  },
});

const ChatBox = ({ inputMessage, setInputMessage, onHandleSend, disabled = false }) => {
  const theme = useTheme();
  const [inputHeight, setInputHeight] = useState(40);
  const [isExceedingLimit, setIsExceedingLimit] = useState(false);
  const inputRef = useRef(null);

  // Check if input exceeds character limit
  const handleTextChange = (text) => {
    setInputMessage(text);
    setIsExceedingLimit(text.length > MAX_CHAR_LIMIT);
  };

  // Handle send button press
  const handleSend = () => {
    if (inputMessage.trim() && !isExceedingLimit) {
      onHandleSend();
    }
  };

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <View style={styles.inputContainer}>
        <PaperTextInput
          mode="outlined"
          ref={inputRef}
          outlineStyle={[
            styles.outlineStyle,
            {
              borderColor: isExceedingLimit
                ? theme.colors.error
                : disabled
                  ? theme.colors.surfaceDisabled
                  : theme.colors.onBackground,
            }
          ]}
          value={inputMessage}
          onChangeText={handleTextChange}
          numberOfLines={3}
          placeholder={disabled ? "Login or add credits to ask AI..." : "Ask AI…"}
          style={[styles.input, {
            height: inputHeight,
            backgroundColor: disabled ? theme.colors.surfaceDisabled : theme.colors.surface,
          }]}
          textAlignVertical="top"
          multiline
          disabled={disabled}
          onContentSizeChange={(e) => {
            setInputHeight(Math.max(40, e.nativeEvent.contentSize.height));
          }}
        />

        {/* Character count - positioned inside the input container */}
        {inputMessage.length > 0 && (
          <Text style={[
            styles.charCount,
            {
              color: isExceedingLimit ? theme.colors.error : theme.colors.onSurfaceVariant,
              backgroundColor: `${theme.colors.surface}CC` // Semi-transparent background
            }
          ]}>
            {inputMessage.length}/{MAX_CHAR_LIMIT}
          </Text>
        )}

        <IconButton
          containerColor={
            disabled
              ? theme.colors.surfaceDisabled
              : isExceedingLimit
                ? theme.colors.errorContainer
                : (!inputMessage.trim())
                  ? (theme.dark ? theme.colors.surfaceVariant : '#E0E0E0') // Muted color when disabled
                  : theme.colors.primary
          }
          icon="arrow-right"
          iconColor={
            disabled
              ? theme.colors.onSurfaceDisabled
              : isExceedingLimit
                ? theme.colors.error
                : (!inputMessage.trim())
                  ? (theme.dark ? '#FFFFFF80' : '#9E9E9E') // Lighter gray in light mode, semi-transparent white in dark mode
                  : '#FFFFFF'
          }
          onPress={handleSend}
          disabled={disabled || isExceedingLimit || !inputMessage.trim()}
        />
      </View>

      {/* Warning message when exceeding limit */}
      {isExceedingLimit && (
        <Text style={[styles.warningText, { color: theme.colors.error }]}>
          Your message is too long. Please reduce your input to {MAX_CHAR_LIMIT} characters or less for optimal response.
        </Text>
      )}
    </View>
  );
};

export default ChatBox;