import React, { useState } from 'react';
import { View, Text, ScrollView, TouchableOpacity, Alert } from 'react-native';
import { <PERSON>, Button, Divider, useTheme } from 'react-native-paper';
import { useNavigation } from '@react-navigation/native';
import { usePurchase } from '../store/PurchaseContext';
import { useExamContext } from '../store/ExamContext';
/* import RealTimeSyncService from '../services/RealTimeSyncService'; */
import TestFixes from '../debug/TestFixes';
import SyncDebugger from '../debug/SyncDebugger';

const DebugPanel = () => {
  const { colors } = useTheme();
  const navigation = useNavigation();
  const { isSubscriptionActive, userId, purchases, loading } = usePurchase();
  const { selectedExam } = useExamContext();
  const [debugOutput, setDebugOutput] = useState('');

  const addDebugOutput = (message) => {
    const timestamp = new Date().toLocaleTimeString();
    setDebugOutput(prev => `[${timestamp}] ${message}\n${prev}`);
  };

  const clearDebugOutput = () => {
    setDebugOutput('');
  };

  // Navigate to RevenueCat Test Screen
  const openRevenueCatTestScreen = () => {
    try {
      addDebugOutput('Opening RevenueCat Test Screen...');
      navigation.navigate('RevenueCatTest');
    } catch (error) {
      addDebugOutput(`Navigation failed: ${error.message}`);
      Alert.alert(
        'Navigation Error',
        'Could not open RevenueCat Test Screen. Make sure it\'s added to the navigation stack.',
        [{ text: 'OK' }]
      );
    }
  };

  // Test purchase verification
  const testPurchaseVerification = async () => {
    addDebugOutput('=== TESTING PURCHASE VERIFICATION ===');
    addDebugOutput(`User ID: ${userId || 'null'}`);
    addDebugOutput(`Selected Exam: ${selectedExam?.exam_code} (${selectedExam?.id})`);
    addDebugOutput(`Purchases Count: ${purchases?.length || 0}`);
    addDebugOutput(`Loading: ${loading}`);

    if (selectedExam) {
      const hasPremium = await isSubscriptionActive(selectedExam?.exam_code, true);
      addDebugOutput(`Premium Access: ${hasPremium}`);

      // Show available purchases
      purchases?.forEach((purchase, index) => {
        addDebugOutput(`Purchase ${index + 1}: ${purchase.productId} - ${purchase.status}`);
      });
    } else {
      addDebugOutput('No exam selected');
    }
  };

  /* // Test sync service status
  const testSyncService = () => {
    addDebugOutput('=== TESTING SYNC SERVICE ===');
    try {
      RealTimeSyncService.logSyncStatus();
      const status = RealTimeSyncService.getSyncStatus();
      addDebugOutput(`Sync Service Active: ${status.isActive}`);
      addDebugOutput(`User ID: ${status.userId}`);
      addDebugOutput(`Intervals Active: ${JSON.stringify(status.intervals)}`);
      addDebugOutput(`Sync Windows: ${JSON.stringify(status.syncWindows)}`);
    } catch (error) {
      addDebugOutput(`Sync Service Error: ${error.message}`);
    }
  }; */

  // Force sync operations
  const forceSyncOperations = async () => {
    addDebugOutput('=== FORCING SYNC OPERATIONS ===');
    try {
      await RealTimeSyncService.forceSyncAll();
      addDebugOutput('Force sync completed successfully');
    } catch (error) {
      addDebugOutput(`Force sync failed: ${error.message}`);
    }
  };

  // Test QnA deduplication
  const testQnADeduplication = () => {
    addDebugOutput('=== TESTING QNA DEDUPLICATION ===');
    try {
      const result = SyncDebugger.testQnADeduplication();
      addDebugOutput(`QnA Deduplication Test: ${result ? 'PASSED' : 'FAILED'}`);
    } catch (error) {
      addDebugOutput(`QnA Deduplication Error: ${error.message}`);
    }
  };

  // Run comprehensive tests
  const runComprehensiveTests = async () => {
    addDebugOutput('=== RUNNING COMPREHENSIVE TESTS ===');
    try {
      const results = await TestFixes.runAllTests();
      Object.entries(results).forEach(([test, passed]) => {
        addDebugOutput(`${test}: ${passed ? 'PASSED' : 'FAILED'}`);
      });
    } catch (error) {
      addDebugOutput(`Comprehensive tests failed: ${error.message}`);
    }
  };

  // Monitor sync for 30 seconds
  const monitorSyncOperations = () => {
    addDebugOutput('=== MONITORING SYNC OPERATIONS (30 seconds) ===');

    let syncDetected = false;
    const originalConsoleLog = console.log;

    console.log = function(...args) {
      const message = args.join(' ');
      if (message.includes('===== SYNCING') && message.includes('TO MONGODB =====')) {
        syncDetected = true;
        addDebugOutput(`🎯 SYNC DETECTED: ${message}`);
      }
      if (message.includes('[RealTimeSyncService]') && message.includes('INTERVAL TRIGGERED')) {
        addDebugOutput(`⏰ INTERVAL: ${message}`);
      }
      originalConsoleLog.apply(console, args);
    };

    setTimeout(() => {
      console.log = originalConsoleLog;
      if (syncDetected) {
        addDebugOutput('✅ Sync operations detected during monitoring');
      } else {
        addDebugOutput('❌ No sync operations detected during monitoring');
      }
    }, 30000);

    addDebugOutput('Monitoring started... (30 seconds)');
  };

  // Restart sync service
  const restartSyncService = () => {
    addDebugOutput('=== RESTARTING SYNC SERVICE ===');
    try {
      RealTimeSyncService.stop();
      addDebugOutput('Sync service stopped');

      setTimeout(() => {
        if (userId) {
          RealTimeSyncService.setAppActive(true);
          RealTimeSyncService.initialize(userId);
          addDebugOutput('Sync service restarted');
        } else {
          addDebugOutput('Cannot restart - no user ID available');
        }
      }, 1000);
    } catch (error) {
      addDebugOutput(`Restart failed: ${error.message}`);
    }
  };



  return (
    <Card style={{ margin: 16, backgroundColor: colors.surface }}>
      <Card.Title title="Debug Panel" titleStyle={{ color: colors.text }} />
      <Card.Content>
        <View style={{ flexDirection: 'row', flexWrap: 'wrap', gap: 8, marginBottom: 16 }}>
          <Button
            mode="outlined"
            onPress={testPurchaseVerification}
            style={{ borderColor: colors.primary }}
            labelStyle={{ color: colors.primary, fontSize: 12 }}
          >
            Test Purchases
          </Button>

         {/*  <Button
            mode="outlined"
            onPress={testSyncService}
            style={{ borderColor: colors.primary }}
            labelStyle={{ color: colors.primary, fontSize: 12 }}
          >
            Test Sync
          </Button> */}

          <Button
            mode="outlined"
            onPress={forceSyncOperations}
            style={{ borderColor: colors.primary }}
            labelStyle={{ color: colors.primary, fontSize: 12 }}
          >
            Force Sync
          </Button>

          <Button
            mode="outlined"
            onPress={testQnADeduplication}
            style={{ borderColor: colors.primary }}
            labelStyle={{ color: colors.primary, fontSize: 12 }}
          >
            Test QnA
          </Button>

          <Button
            mode="outlined"
            onPress={runComprehensiveTests}
            style={{ borderColor: colors.primary }}
            labelStyle={{ color: colors.primary, fontSize: 12 }}
          >
            Run All Tests
          </Button>

          <Button
            mode="outlined"
            onPress={monitorSyncOperations}
            style={{ borderColor: colors.primary }}
            labelStyle={{ color: colors.primary, fontSize: 12 }}
          >
            Monitor Sync
          </Button>

          <Button
            mode="outlined"
            onPress={restartSyncService}
            style={{ borderColor: colors.primary }}
            labelStyle={{ color: colors.primary, fontSize: 12 }}
          >
            Restart Sync
          </Button>

          <Button
            mode="contained"
            onPress={openRevenueCatTestScreen}
            style={{ backgroundColor: colors.secondary }}
            labelStyle={{ color: colors.onSecondary, fontSize: 12 }}
          >
            🧪 RevenueCat Test
          </Button>

          <Button
            mode="outlined"
            onPress={clearDebugOutput}
            style={{ borderColor: colors.error }}
            labelStyle={{ color: colors.error, fontSize: 12 }}
          >
            Clear
          </Button>
        </View>

        <Divider style={{ marginVertical: 8 }} />

        <Text style={{ color: colors.text, fontWeight: 'bold', marginBottom: 8 }}>
          Debug Output:
        </Text>

        <ScrollView
          style={{
            height: 200,
            backgroundColor: colors.background,
            padding: 8,
            borderRadius: 4,
            borderWidth: 1,
            borderColor: colors.outline
          }}
          showsVerticalScrollIndicator={true}
        >
          <Text style={{
            color: colors.text,
            fontFamily: 'monospace',
            fontSize: 12,
            lineHeight: 16
          }}>
            {debugOutput || 'No debug output yet. Click a button to start testing.'}
          </Text>
        </ScrollView>

        <View style={{ marginTop: 16 }}>
          <Text style={{ color: colors.text, fontSize: 12, fontStyle: 'italic' }}>
            Current Status: User ID: {userId || 'null'} | Purchases: {purchases?.length || 0} |
            Exam: {selectedExam?.exam_code || 'none'}
          </Text>
        </View>
      </Card.Content>
    </Card>
  );
};

export default DebugPanel;
