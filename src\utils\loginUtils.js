/**
 * Utility functions for handling login-related operations
 */
import { Alert } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { GoogleSignin } from '@react-native-google-signin/google-signin';

import apiClient from '../services/ApiClient';

/**
 * Handles the post-login flow consistently across all login points
 * - Updates user in PurchaseContext with minimal delay
 * - Efficiently checks premium access status
 * - Loads appropriate QnA content based on premium status
 *
 * @param {Object} params - Parameters object
 * @param {Object} params.selectedExam - The currently selected exam
 * @param {Function} params.isSubscriptionActive - Function to check if subscription is active
 * @param {Function} params.loadQnA - Function to load QnA questions
 * @param {Function} params.refreshPurchases - Function to refresh purchases (optional)
 * @param {Function} params.updateUser - Function to update user in PurchaseContext (optional)
 * @param {String} params.userId - User ID (optional, required if updateUser is provided)
 * @param {Function} params.onComplete - Callback function to execute after login flow completes (optional)
 * @param {Number} params.delayMs - Delay in milliseconds before checking subscription (default: 200ms)
 * @returns {Promise<Object>} - Object containing success status and premium access status
 */
export const handlePostLoginFlow = async ({
  selectedExam,
  isSubscriptionActive,
  loadQnA,
  refreshPurchases,
  updateUser,
  userId,
  onComplete,
  clearPurchaseLoadTracking,
  delayMs = 200
}) => {
  try {
    console.log('[LOGINSYNC] Starting login flow for user ID:', userId || 'unknown');

    // Clear purchase load tracking if available
    if (clearPurchaseLoadTracking && typeof clearPurchaseLoadTracking === 'function') {
      console.log('[LOGINSYNC] Clearing purchase load tracking in login flow');
      clearPurchaseLoadTracking();
    }

    if (!userId) {
      console.warn('[LOGINSYNC] No user ID provided, login may not be complete');
    }

    // Add a minimal delay to ensure login is processed
    await new Promise(resolve => setTimeout(resolve, delayMs));

    // Update user in PurchaseContext if userId is provided
    // This is the most efficient way to update purchase state
    if (updateUser && userId) {
      console.log('[LOGINSYNC] Updating user in PurchaseContext:', userId);
      await updateUser(userId);

      // Add extra delay to ensure PurchaseContext state is fully updated
      console.log('[LOGINSYNC] Waiting for PurchaseContext state to stabilize...');
      await new Promise(resolve => setTimeout(resolve, 500));
    } else if (refreshPurchases && typeof refreshPurchases === 'function') {
      // Fall back to refreshPurchases if updateUser is not available
      console.log('[LOGINSYNC] Refreshing purchases');
      await refreshPurchases();

      // Add delay for purchase refresh as well
      await new Promise(resolve => setTimeout(resolve, 300));
    }

    // Check if there's a selected exam
    if (!selectedExam) {
      console.log('[LOGINSYNC] No selected exam found');
      if (onComplete) onComplete({ success: true, hasPremiumAccess: false });
      return { success: true, hasPremiumAccess: false };
    }

    // Verify isSubscriptionActive function is available
    if (typeof isSubscriptionActive !== 'function') {
      console.error('[LOGINSYNC] isSubscriptionActive is not a function');
      throw new Error('isSubscriptionActive function is not available');
    }

    // Check premium access status - force a fresh check by passing true
    // Add a small delay to ensure purchase data is fully processed
    await new Promise(resolve => setTimeout(resolve, 200));

    // Normalize the exam ID
    const examId = String(selectedExam.id).trim();
    const isMlaExam = examId.toLowerCase().includes('mla-c01') ||
                      (selectedExam.exam_code && selectedExam.exam_code.toLowerCase().includes('mla-c01'));

    if (isMlaExam) {
      console.log(`[LOGINSYNC] Special handling for MLA-C01 exam (${examId})`);
    }

    let hasPremiumAccess = await isSubscriptionActive(selectedExam.exam_code, true);
    console.log(`[LOGINSYNC] Premium access for ${selectedExam.exam_code} (${examId}): ${hasPremiumAccess}`);

    // Double-check premium access if it's false but we have purchases
    if (!hasPremiumAccess) {
      console.log(`[LOGINSYNC] Premium access check returned false, double-checking...`);
      await new Promise(resolve => setTimeout(resolve, 200));

      // Try again with force refresh
      hasPremiumAccess = await isSubscriptionActive(examCode, true);
      console.log(`[LOGINSYNC] Double-check premium access result: ${hasPremiumAccess}`);

      // If still false and this is MLA-C01, try a special check
      if (!hasPremiumAccess && isMlaExam) {
        console.log(`[LOGINSYNC] Special MLA-C01 check - trying alternative ID formats`);

        // Try alternative formats for MLA-C01
        const alternativeIds = [
          'mla-c01',
          'MLA-C01',
          'mla_c01',
          'MLA_C01',
          'mlac01',
          'MLAC01'
        ];

        for (const altId of alternativeIds) {
          const altCheck = await isSubscriptionActive(examCode, true);
          console.log(`[LOGINSYNC] MLA-C01 alt check (${altId}): ${altCheck}`);

          if (altCheck) {
            hasPremiumAccess = true;
            console.log(`[LOGINSYNC] Found premium access with alternative ID: ${altId}`);
            break;
          }
        }
      }
    }

    // Load QnA data based on premium access status
    // We'll set a flag to indicate that we've loaded QnA data here
    let qnaLoaded = false;

    // Generate a unique ID for this login flow
    const flowId = Math.random().toString(36).substring(2, 8);

    if (typeof loadQnA === 'function') {
      // Check if there's a recent QnA request in the ApiClient tracker for BOTH free and premium content
      // This prevents duplicate requests when QnAContext already loaded content via AsyncStorage listener
      const hasPremiumRequest = apiClient.qnaRequestTracker?.check(selectedExam.exam_code, false, 10000);
      const hasFreeRequest = apiClient.qnaRequestTracker?.check(selectedExam.exam_code, true, 10000);

      if (hasPremiumRequest || hasFreeRequest) {
        console.log(`[LOGINSYNC] [${flowId}] Skipping QnA load during login flow - found recent request in ApiClient tracker (premium: ${hasPremiumRequest}, free: ${hasFreeRequest})`);
        qnaLoaded = true; // Mark as loaded since we know it was recently loaded
      } else {
        console.log(`[LOGINSYNC] [${flowId}] No recent QnA requests found in tracker, proceeding with load`);
        if (hasPremiumAccess) {
          // User has premium access, load premium content
          console.log(`[LOGINSYNC] [${flowId}] Loading PREMIUM content for: ${selectedExam.exam_code}`);
          await loadQnA(selectedExam.exam_code, false, selectedExam.id);
          qnaLoaded = true;
        } else {
          // User does not have premium access, load free content
          console.log(`[LOGINSYNC] [${flowId}] Loading FREE content for: ${selectedExam.exam_code}`);
          await loadQnA(selectedExam.exam_code, true, selectedExam.id);
          qnaLoaded = true;
        }
      }
    } else {
      console.error('[LOGINSYNC] loadQnA function is not available');
    }

    // Pass the qnaLoaded flag to the completion callback
    const result = { success: true, hasPremiumAccess, qnaLoaded };

    // Call the completion callback if provided
    if (onComplete) {
      onComplete(result);
    }

    console.log('[LOGINSYNC] Post-login flow completed');
    return result;
  } catch (error) {
    console.error('[LOGINSYNC] Error in post-login flow:', error);

    // Call the completion callback with error if provided
    if (onComplete) {
      onComplete({ success: false, error });
    }

    return { success: false, error };
  }
};

/**
 * Handles the BuyNowModal login flow
 * - Efficiently checks if user already has premium access to the target exam
 * - Closes modal and navigates to Home if premium access exists
 * - Loads QnA with explicit is_free=false parameter
 *
 * @param {Object} params - Parameters object
 * @param {Object} params.targetExam - The exam being purchased
 * @param {Function} params.isSubscriptionActive - Function to check if subscription is active
 * @param {Function} params.loadQnA - Function to load QnA questions
 * @param {Function} params.setBuyNowModalVisible - Function to control modal visibility
 * @param {Object} params.navigation - Navigation object for screen transitions
 * @param {Function} params.setLoading - Function to control loading state (optional)
 * @param {Function} params.refreshPurchases - Function to refresh purchases (optional)
 * @returns {Promise<Object>} - Object containing success status and premium access status
 */
export const handleBuyNowModalLoginFlow = async ({
  targetExam,
  isSubscriptionActive,
  loadQnA,
  setBuyNowModalVisible,
  navigation,
  setLoading,
  refreshPurchases
}) => {
  try {
    // Set loading state if provided
    if (setLoading) setLoading(true);

    console.log('[LOGINSYNC] Checking premium access in BuyNowModal');

    // Refresh purchases to ensure we have the latest data
    if (typeof refreshPurchases === 'function') {
      await refreshPurchases();

      // Add a small delay to ensure purchases are fully loaded
      await new Promise(resolve => setTimeout(resolve, 300));
    }

    // Check if there's a target exam
    if (!targetExam) {
      if (setLoading) setLoading(false);
      return { success: true, hasPremiumAccess: false, shouldContinue: true };
    }

    // Get exam ID
    const examId = targetExam.id;

    console.log(`[LOGINSYNC] Checking premium access for exam: ${targetExam.exam_code} (ID: ${examId})`);

    // Verify isSubscriptionActive function is available
    if (typeof isSubscriptionActive !== 'function') {
      console.error('[LOGINSYNC] isSubscriptionActive function is not available');
      throw new Error('isSubscriptionActive function is not available');
    }

    // Check if user already has premium access - force a fresh check
    console.log(`[LOGINSYNC] Calling isSubscriptionActive with examId: ${examId}`);

    // Add a longer delay to ensure purchases are fully loaded
    console.log(`[LOGINSYNC] Adding additional delay before premium access check`);
    await new Promise(resolve => setTimeout(resolve, 500));

    // Double-check the examId format
    if (typeof examId !== 'string' && typeof examId !== 'number') {
      console.error(`[LOGINSYNC] Invalid examId format: ${typeof examId}`, examId);
      return { success: false, hasPremiumAccess: false, error: 'Invalid examId format' };
    }

    const hasPremiumAccess = await isSubscriptionActive(targetExam.exam_code, true);
    console.log(`[LOGINSYNC] Premium access for ${targetExam.exam_code} (${examId}): ${hasPremiumAccess}`);

    // If user already has premium access
    if (hasPremiumAccess) {
      // 1. Close the modal first
      if (setBuyNowModalVisible) {
        setBuyNowModalVisible(false);
      }

      // 2. Load premium content
      if (typeof loadQnA === 'function' && targetExam.exam_code) {
        await loadQnA(targetExam.exam_code, false, examId);
      }

      // 3. Navigate to Home
      if (navigation) {
        navigation.reset({
          index: 0,
          routes: [{ name: 'MainTabs' }],
        });
      }

      // 4. Display alert
      setTimeout(() => {
        Alert.alert(
          'Premium Access',
          'You already have premium access to this exam',
          [{ text: 'OK' }]
        );
      }, 300);

      if (setLoading) setLoading(false);
      return { success: true, hasPremiumAccess: true, shouldContinue: false };
    }

    // If user doesn't have premium access, continue with normal purchase flow
    if (setLoading) setLoading(false);
    return { success: true, hasPremiumAccess: false, shouldContinue: true };
  } catch (error) {
    console.error('[LOGINSYNC] Error in BuyNowModal login flow:', error);
    if (setLoading) setLoading(false);
    return { success: false, error, shouldContinue: true };
  }
};

/**
 * Clear all user-related data from AsyncStorage
 * @param {string} userId - The user ID to clear data for
 * @returns {Promise<void>}
 */
export const clearUserData = async (userId) => {
  try {
    // Base keys to remove
    const keysToRemove = ['user', 'apires', 'loginStateChanged', 'google_id_token'];

    // Add user-specific purchase data if userId exists
    if (userId) {
      keysToRemove.push(`purchases_${userId}`);
      console.log(`[AUTH] Including purchases_${userId} in keys to remove`);
    }

    // Clear AsyncStorage
    console.log(`[AUTH] Removing keys from AsyncStorage: ${keysToRemove.join(', ')}`);
    await AsyncStorage.multiRemove(keysToRemove);

    // Double-check that user data is really gone
    const checkUser = await AsyncStorage.getItem('user');
    if (checkUser) {
      console.log(`[AUTH] User data still exists after multiRemove, trying again with direct removal`);
      await AsyncStorage.removeItem('user');
    }

    // Double-check that purchase data is really gone
    if (userId) {
      const checkPurchases = await AsyncStorage.getItem(`purchases_${userId}`);
      if (checkPurchases) {
        console.log(`[AUTH] Purchase data still exists after multiRemove, trying again with direct removal`);
        await AsyncStorage.removeItem(`purchases_${userId}`);
      }
    }

    console.log('[AUTH] User data cleared successfully');
  } catch (error) {
    console.error('[AUTH] Error clearing user data:', error);
    throw error;
  }
};

/**
 * Notify all components about login state change
 * @param {boolean} isLoggedIn - Whether the user is logged in
 * @param {Object} userData - User data (optional)
 * @returns {Promise<void>}
 */
export const notifyLoginStateChange = async (isLoggedIn, userData = null) => {
  try {
    // Store the event for other components to check
    const eventData = {
      timestamp: Date.now(),
      isLoggedIn,
      userId: userData?.id || null,
      userEmail: userData?.email || null,
      userName: userData?.name || null
    };

    console.log('[AUTH] Notifying login state change:', isLoggedIn ? 'Logged in' : 'Logged out');
    await AsyncStorage.setItem('loginStateChanged', JSON.stringify(eventData));
  } catch (error) {
    console.error('[AUTH] Error storing login state change:', error);
  }
};

/**
 * Complete logout process
 * @param {Object} options - Logout options
 * @param {Function} options.clearPurchases - Function to clear purchases state
 * @param {Function} options.clearCache - Function to clear API cache
 * @param {Function} options.loadQnA - Function to load QnA data
 * @param {Object} options.currentExam - Current exam object
 * @returns {Promise<boolean>} - Whether logout was successful
 */
export const performLogout = async ({
  clearPurchases,
  clearCache,
  loadQnA,
  currentExam,
  pendingExamCodeRef
}) => {
  try {
    console.log('[AUTH] ====== LOGOUT STARTED ======');

    // Get user ID before logout
    let userId = null;
    try {
      const userJson = await AsyncStorage.getItem('user');
      if (userJson) {
        const userData = JSON.parse(userJson);
        userId = userData.id;
      }
    } catch (error) {
      console.error('[AUTH] Error getting user ID before logout:', error);
    }

    // Sign out from Google
    try {
      await GoogleSignin.signOut();
      console.log('[AUTH] Google Sign-Out successful');
    } catch (error) {
      console.error('[AUTH] Google Sign-Out error:', error);
      // Continue with logout even if Google Sign-Out fails
    }

    // Clear user data from AsyncStorage
    await clearUserData(userId);

    // Clear purchases state if function provided
    if (typeof clearPurchases === 'function') {
      console.log('[AUTH] Clearing purchases state');
      await clearPurchases();
    }

    // Adapty logout removed - no longer using Adapty SDK
    console.log('[AUTH] Skipping Adapty logout - SDK removed');

    // Clear API cache if function provided
    if (typeof clearCache === 'function') {
      console.log('[AUTH] Clearing API cache');
      clearCache();
    }

    // Reset pending exam code ref if provided
    if (pendingExamCodeRef && typeof pendingExamCodeRef.current !== 'undefined') {
      console.log('[AUTH] Resetting pending exam code ref');
      pendingExamCodeRef.current = null;
    }

    // Notify about login state change
    await notifyLoginStateChange(false);

    // Reload QnA with free content if current exam exists
    if (currentExam && currentExam.exam_code && typeof loadQnA === 'function') {
      console.log('[AUTH] Reloading QnA with free content');

      // Add a small delay to ensure logout is fully processed
      await new Promise(resolve => setTimeout(resolve, 300));

      try {
        await loadQnA(currentExam.exam_code, true, currentExam.id);
        console.log('[AUTH] QnA reload successful');
      } catch (error) {
        console.error('[AUTH] Error reloading QnA:', error);
        // Continue with logout even if QnA reload fails
      }
    }

    console.log('[AUTH] ====== LOGOUT COMPLETED ======');
    return true;
  } catch (error) {
    console.error('[AUTH] Logout error:', error);
    return false;
  }
};
