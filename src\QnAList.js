import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { useFocusEffect } from '@react-navigation/native';
import { FlatList, View, TouchableOpacity } from 'react-native';
import {
  Text, Card, IconButton, useTheme, Appbar, Searchbar,
  Button, ActivityIndicator
} from 'react-native-paper';
import { useNavigation, useRoute } from '@react-navigation/native';
import Icon from 'react-native-vector-icons/Ionicons';
import { useFilters } from './store/FilterContext';
import { useQnAContext } from './store/QnAContext';
import { useUserProgress } from './store/UserProgressContext';
import { useExamContext } from './store/ExamContext';
import { usePurchase } from './store/PurchaseContext';
import { useLogin } from './store/LoginContext';
import StickyBottomAdMob from './components/StickyBottomAdMob';
import { useSubscriptionRefresh } from './utils/subscriptionUtils';
import AICreditBadge from './components/AICreditBadge';
import AICreditModal from './components/AICreditModal';

const TabBar = React.memo(({ tabs, filter, setFilter, colors }) => (
  <View style={{ marginBottom: 16 }}>
    <View style={{
      flexDirection: 'row',
      width: '100%',
      justifyContent: 'space-between',
      gap: 10,
      padding: 10,
      paddingBottom: 0,
      backgroundColor: colors.surfaceVariant,
    }}>
      {tabs.map(tab => (
        <TouchableOpacity
          key={tab.value}
          onPress={() => setFilter(tab.value)}
          style={{
            paddingBottom: 8,
            borderBottomWidth: filter === tab.value ? 3 : 0,
            borderBottomColor: colors.primary,
            alignItems: 'center',
            justifyContent: 'center',
            flex: 1,
          }}>
          <Text style={{
            color: filter === tab.value ? colors.primary : colors.onSurface,
            fontWeight: '500',
            fontSize: 13,
          }}>
            {tab.label}
          </Text>
          <Text style={{
            color: filter === tab.value ? colors.primary : colors.onSurface,
            fontWeight: 'bold',
            fontSize: 18,
          }}>
            {tab.count}
          </Text>
        </TouchableOpacity>
      ))}
    </View>
  </View>
));

const FilterButton = React.memo(({ onPress, hasActiveFilters, colors }) => (
  <TouchableOpacity
    style={{
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      marginBottom: 10,
      gap: 2,
      position: 'relative',
    }}
    onPress={onPress}>
    <Icon
      name="filter"
      size={20}
      color={colors.primary}
      style={{ marginRight: 5 }}
    />
    <Text style={{ color: colors.primary, fontSize: 16, fontWeight: 'bold' }}>
      Filter
    </Text>
    {hasActiveFilters && (
      <View style={{
        width: 8,
        height: 8,
        borderRadius: 4,
        backgroundColor: colors.primary,
        position: 'absolute',
        top: 0,
        right: 0,
      }} />
    )}
  </TouchableOpacity>
));

const QuestionCard = React.memo(({
  item,
  index,
  colors,
  navigation,
  handleToggleBookmark,
  filter,
  sortedData,
  loadingCardId,
  setLoadingCardId
}) => {
  const tabLabel = useMemo(() => {
    return filter === 'all' ? 'All' :
      filter === 'lastIncorrect' ? 'Incorrect' :
        filter === 'notBrowsed' ? 'Unbrowsed' : 'Bookmarked';
  }, [filter]);

  const isLoading = loadingCardId === item.id;

  return (
    <Card
      style={{
        marginBottom: 16,
        backgroundColor: colors.surface,
        position: 'relative',
        marginHorizontal: 16,
        opacity: loadingCardId && !isLoading ? 0.6 : 1, // Dim other cards when one is loading
      }}
      onPress={() => {
        if (loadingCardId) return; // Prevent clicks while loading
        setLoadingCardId(item.id);

        // Add a small delay to show the loading state
        setTimeout(() => {
          navigation.navigate('QnADetail', {
            filteredData: sortedData,
            initialIndex: index,
            activeTabLabel: tabLabel,
            fromQuizResult: false,
          });
          // Reset loading state after navigation
          setLoadingCardId(null);
        }, 10);
      }}>
      {isLoading ? (
        <View style={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          justifyContent: 'center',
          alignItems: 'center',
          backgroundColor: 'rgba(0,0,0,0.05)',
          zIndex: 2,
          borderRadius: 8,
        }}>
          <ActivityIndicator size="large" color={colors.primary} />
        </View>
      ) : null}
      <IconButton
        icon={item.bookmarked ? 'bookmark' : 'bookmark-outline'}
        onPress={() => handleToggleBookmark(item.id, item.subject)}
        iconColor={colors.primary}
        size={20}
        style={{
          position: 'absolute',
          right: 0,
          top: 0,
          zIndex: 1,
          backgroundColor: colors.surface,
        }}
      />
      <Card.Content style={{ padding: 16 }}>
        <Text
          variant="labelSmall"
          style={{
            color: colors.onSurfaceVariant,
            fontSize: 12,
            width: '90%',
            opacity: 0.8,
          }}>
          {item.subjectName}
        </Text>
        <Text
          variant={'bodyLarge'}
          style={{
            color: colors.onSurface,
            marginBottom: 8,
            lineHeight: 20,
            fontWeight: '600',
            marginTop: 5,
          }}>
          {item.first_sentence}
        </Text>
      </Card.Content>
    </Card>
  );
});

const QnAListPage = () => {
  const [creditModalVisible, setCreditModalVisible] = useState(false);
  const { colors } = useTheme();
  const navigation = useNavigation();
  const route = useRoute();
  const { setAppliedFilters, appliedFilters } = useFilters();
  const { progress } = useUserProgress();
  const { selectedExam } = useExamContext();
  const { questionsBySubject } = useQnAContext();

  const [filter, setFilter] = useState('all');
  const [search, setSearch] = useState('');
  const [loadingCardId, setLoadingCardId] = useState(null);

  const { subscriptionActive, getSubscriptionsInfo } = usePurchase();
  const { isLoggedIn } = useLogin() || {};

  useSubscriptionRefresh();

  // Memoized route parameters parsing
  const { subjectCode, examSubjects } = useMemo(() => {
    const { subjectCode: routeSubjectCode } = route.params;
    return {
      subjectCode: parseInt(routeSubjectCode, 10) || 0,
      examSubjects: selectedExam?.subjects || []
    };
  }, [route.params, selectedExam]);

  // Memoized questions data processing
  const { dataWithIds, subjectFilteredData } = useMemo(() => {
    const questions = appliedFilters.subjects?.length
      ? appliedFilters.subjects.flatMap(code => {
        // Convert code to string to match potential string keys in questionsBySubject
        const key = String(code);
        return questionsBySubject[key] || [];
      })
      : Object.values(questionsBySubject).flat();

    const processed = questions.map((question, originalIndex) => {
      return {
        ...question,
        originalIndex,
        id: question._id,
        subject: Number(question.subject),
        subjectName: examSubjects[Number(question.subject) - 1],
        bookmarked: selectedExam?.id
          ? progress[selectedExam.id]?.[question.subject]?.bookmarked?.find(b => b.id === question._id)?.timestamp || 0
          : 0,
        // Get last incorrect timestamp
        last_incorrect: selectedExam?.id
          ? progress[selectedExam.id]?.[question.subject]?.incorrect?.find(b => b.id === question._id)?.timestamp || 0
          : 0,
        // Get last browsed timestamp
        last_browsed: selectedExam?.id
          ? progress[selectedExam.id]?.[question.subject]?.browsed?.find(b => b.id === question._id)?.timestamp || 0
          : 0,
        created: question.createdAt || 0,
        question: question.exam.question || '',
        answer: question.exam.answer || '',
        choices: question.exam.choices || '',
        ai_explanation: question.ai_explanation || question.exam.ai_explanation || '',
      };
    });

    /*     console.log('All incorrect entries:', progress[selectedExam?.id]?.[5]?.incorrect);
     */
    const targetId = "/discussions/amazon/view/78676-exam-aws-certified-sysops-administrator-associate-topic-1";
    const foundEntry = progress[selectedExam?.id]?.[5]?.incorrect?.find(b => b.id === targetId);
    /*
        console.log('Found entry:', foundEntry);
        console.log('Found timestamp:', foundEntry?.timestamp || 'Not found');
    
        console.log('progress:', progress)
    
        console.log('processed:', processed) */

    const filtered = appliedFilters.subjects?.length
      ? processed.filter(q => appliedFilters.subjects.includes(q.subject))
      : processed;

    /* console.log('filtered after processed:', filtered) */

    return { dataWithIds: processed, subjectFilteredData: filtered };
  }, [questionsBySubject, appliedFilters, progress, examSubjects, selectedExam]);

  // Memoized tab counts
  const [tabs, hasActiveFilters] = useMemo(() => {
    const counts = {
      all: subjectFilteredData.length,
      bookmarked: subjectFilteredData.filter(item => item.bookmarked).length,
      incorrect: subjectFilteredData.filter(item => item.last_incorrect).length,
      unbrowsed: subjectFilteredData.filter(item => !item.last_browsed).length,
    };

    return [
      [
        { value: 'all', label: 'All', count: counts.all },
        { value: 'bookmarked', label: 'Bookmarked', count: counts.bookmarked },
        { value: 'lastIncorrect', label: 'Incorrect', count: counts.incorrect },
        { value: 'notBrowsed', label: 'Unbrowsed', count: counts.unbrowsed },
      ],
      appliedFilters.subjects?.length > 0 && appliedFilters.subjects?.length != selectedExam?.subjects.length /* || !!appliedFilters.dateRange */
    ];
  }, [subjectFilteredData, appliedFilters]);

  // Filtered and sorted data
  const sortedData = useMemo(() => {
    const filtered = subjectFilteredData.filter(item => {
      if (filter === 'bookmarked' && !item.bookmarked) return false;
      if (filter === 'lastIncorrect' && !item.last_incorrect) return false;
      if (filter === 'notBrowsed' && item.last_browsed) return false;
      if (search && !item.first_sentence.toLowerCase().includes(search.toLowerCase())) return false;
      if (appliedFilters.dateRange?.startDate && appliedFilters.dateRange?.endDate) {
        const itemDate = new Date(item.created * 1000);
        const { startDate, endDate } = appliedFilters.dateRange;
        return itemDate >= startDate && itemDate <= endDate;
      }
      return true;
    });
    /* console.log('filtered:', filtered) */

    if (filter === 'bookmarked') {
      return [...filtered].sort((a, b) => (b.bookmarked || 0) - (a.bookmarked || 0));
    }
    if (filter === 'lastIncorrect') {
      return [...filtered].sort((a, b) => (b.last_incorrect || 0) - (a.last_incorrect || 0));
    }
    /* console.log('sorted filtered:', filtered) */
    return filtered;
  }, [subjectFilteredData, filter, search, appliedFilters.dateRange]);

  const { toggleBookmark } = useUserProgress();
  const handleToggleBookmark = useCallback(
    (questionId, subject) => {
      if (!selectedExam?.id) return;
      toggleBookmark(selectedExam.id, subject, questionId);
    },
    [toggleBookmark, selectedExam]
  );


  useEffect(() => {
    const { initialStartDate, initialEndDate } = route.params || {};
    if (subjectCode) {
      setAppliedFilters(prev => ({ ...prev, subjects: [subjectCode] }));
    }

    // Existing date handling
    if (initialStartDate && initialEndDate) {
      const startDate = new Date(initialStartDate);
      const endDate = new Date(initialEndDate);
      if (!isNaN(startDate) && !isNaN(endDate)) {
        setAppliedFilters(prev => ({
          ...prev,
          dateRange: { startDate, endDate }
        }));
      }
    } else {
      setAppliedFilters(prev => {
        const { dateRange, ...rest } = prev;
        return rest;
      });
    }
  }, [route.params, subjectCode, setAppliedFilters]);

  const [adError, setAdError] = useState(null);

  return (
    <View style={{ flex: 1, backgroundColor: colors.background }}>

      <Appbar.Header elevated>
        <Appbar.BackAction onPress={() => navigation.goBack()} />
        <Appbar.Content title="Q&A List" />
        <Button
          mode="text"
          onPress={() => navigation.navigate('Welcome2')}
          labelStyle={{ color: colors.primary, fontWeight: '600', fontSize: 14 }}
          compact
          style={{ marginRight: 8 }}
        >
          Browse Exams
        </Button>
        <AICreditBadge onPress={() => setCreditModalVisible(true)} />
      </Appbar.Header>

      <TabBar tabs={tabs} filter={filter} setFilter={setFilter} colors={colors} />

      <View style={{ flexDirection: 'row', gap: 10, marginBottom: 16, paddingHorizontal: 16 }}>
        <Searchbar
          placeholder="Search Questions"
          value={search}
          onChangeText={setSearch}
          style={{ flex: 1, borderRadius: 8 }}
        />
        <FilterButton
          onPress={() => navigation.navigate('QnAFilter')}
          hasActiveFilters={hasActiveFilters}
          colors={colors}
        />
      </View>
      <FlatList
        data={sortedData}
        keyExtractor={item => item.id}
        contentContainerStyle={{ paddingVertical: 10 }}
        ListEmptyComponent={
          <View style={{ padding: 20, alignItems: 'center' }}>
            <Text style={{ color: colors.onSurfaceVariant }}>
              No questions match the current filters.
            </Text>
          </View>
        }
        renderItem={({ item, index }) => (
          <QuestionCard
            item={item}
            index={index}
            colors={colors}
            navigation={navigation}
            handleToggleBookmark={handleToggleBookmark}
            filter={filter}
            sortedData={sortedData}
            loadingCardId={loadingCardId}
            setLoadingCardId={setLoadingCardId}
          />
        )}
      />
      <StickyBottomAdMob subscriptionActive={subscriptionActive} />
      <AICreditModal 
        visible={creditModalVisible}
        onDismiss={() => setCreditModalVisible(false)}
      />
    </View>
  );
};

export default QnAListPage;