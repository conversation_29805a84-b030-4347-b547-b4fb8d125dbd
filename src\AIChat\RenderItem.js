import React, { useState } from 'react';
import { View, StyleSheet, ToastAndroid, Platform, Alert, ActionSheetIOS, TouchableOpacity } from 'react-native';
import Clipboard from '@react-native-clipboard/clipboard';
import { Avatar, useTheme, ActivityIndicator, Text, Menu } from 'react-native-paper';

const RenderMessage = ({ item, loading, onDeleteMessage, onRetry }) => {
  const theme = useTheme();
  const isUser = item.choices[0].message.role === 'user';
  const messageContent = item.choices[0].message.content || '';
  const isLoading = item.choices[0].message.isLoading || false;
  const isError = item.choices[0].message.isError || false;
  const [menuVisible, setMenuVisible] = useState(false);
  const [menuPosition, setMenuPosition] = useState({ x: 0, y: 0 });

  // Function to copy text to clipboard
  const copyToClipboard = (text) => {
    try {
      Clipboard.setString(text);
      // Show toast on Android
      if (Platform.OS === 'android') {
        ToastAndroid.show('Text copied to clipboard', ToastAndroid.SHORT);
      } else {
        // Show alert on iOS
        Alert.alert('Copied', 'Text copied to clipboard');
      }
    } catch (error) {
      console.error('Failed to copy text: ', error);
    }
  };

  // Function to handle message deletion
  const handleDelete = () => {
    // Don't allow deleting the welcome message
    if (item.id === 'welcome-message') {
      return;
    }

    // Call the onDeleteMessage prop function
    if (onDeleteMessage) {
      onDeleteMessage(item.id);
    }

    // Close the menu
    setMenuVisible(false);
  };

  // Function to show context menu
  const showContextMenu = (event) => {
    // Get the position of the long press
    const { pageX, pageY } = event.nativeEvent;

    if (Platform.OS === 'ios') {
      // Use ActionSheetIOS for iOS
      ActionSheetIOS.showActionSheetWithOptions(
        {
          options: ['Copy', 'Delete', 'Cancel'],
          destructiveButtonIndex: 1,
          cancelButtonIndex: 2,
        },
        (buttonIndex) => {
          if (buttonIndex === 0) {
            // Copy
            copyEntireMessage();
          } else if (buttonIndex === 1) {
            // Delete
            handleDelete();
          }
        }
      );
    } else {
      // Show menu for Android
      setMenuPosition({ x: pageX, y: pageY });
      setMenuVisible(true);
    }
  };

  // Add debug logging to see what's coming in
  /* console.log('Rendering message:', JSON.stringify({
    isUser,
    content: messageContent.substring(0, 50) + (messageContent.length > 50 ? '...' : ''),
    hasMetadata: !!item.choices[0].message.metadata,
    metadata: item.choices[0].message.metadata
  })); */

  const styles = StyleSheet.create({
    messageContainer: {
      padding: 10,
      borderRadius: 10,
      marginVertical: 4,
      marginHorizontal: 10,
      paddingBottom: 12, // Consistent padding at the bottom
    },
    userMessage: {
      alignSelf: 'flex-end',
      backgroundColor: theme.colors.primary,
    },
    assistantMessage: {
      alignSelf: 'flex-start',
      backgroundColor: theme.colors.gray,
    },
    errorMessage: {
      alignSelf: 'flex-start',
      backgroundColor: '#FFEBEE', // Light red background for errors
      borderLeftWidth: 4,
      borderLeftColor: '#D32F2F', // Red border
    },
    assistanceMessageText: {
      color: theme.colors.assistanceText,
      fontSize: 16,
      lineHeight: 22, // Improved line height for readability
    },
    userMessageText: {
      color: '#FFFFFF', // Explicitly set white text for user messages
      fontSize: 16,
      lineHeight: 22, // Improved line height for readability
    },
    assistantIcon: {
      flexDirection: 'row',
      marginTop: 10,
      marginLeft: 10,
    },
    avatar: {
      marginRight: 7,
    },
    loading: {
      marginLeft: 10,
    },
    boldTextHeading: {
      fontWeight: 'bold',
      fontSize: 18,
    },
    assistanceText: {
      fontSize: 16,
      fontFamily: 'Medium',
      color: theme.colors.assistanceText,
    },
    errorText: {
      color: '#D32F2F', // Red text for errors
      fontSize: 16,
      lineHeight: 22,
    },
    errorIcon: {
      color: '#D32F2F',
    },
    retryContainer: {
      marginTop: 12,
      alignItems: 'flex-end',
    },
    retryButton: {
      backgroundColor: '#D32F2F',
      paddingHorizontal: 16,
      paddingVertical: 8,
      borderRadius: 20,
    },
    retryButtonText: {
      color: '#FFFFFF',
      fontWeight: 'bold',
    }
  });

  const renderContent = () => {
    if (item.choices[0].message.metadata) {
      const { formattedAnswer = '' } = item.choices[0].message.metadata || {};

      // Split by paragraphs (double newlines)
      const paragraphs = formattedAnswer.split(/\n\n+/);

      return (
        <View style={{width: '100%'}}>
          {paragraphs.map((paragraph, paragraphIndex) => (
            <View
              key={`paragraph-${paragraphIndex}`}
              style={{
                marginBottom: paragraphIndex < paragraphs.length - 1 ? 24 : 0 // More space between paragraphs, no space after last
              }}
            >
              {paragraph.split('\n').map((line, lineIndex) => (
                <Text
                  key={`line-${paragraphIndex}-${lineIndex}`}
                  style={[
                    styles.assistanceMessageText,
                    { marginBottom: lineIndex < paragraph.split('\n').length - 1 ? 8 : 0 } // Space between lines, no space after last
                  ]}
                  selectable={true}
                  onLongPress={() => copyToClipboard(line)}
                >
                  {line.split(/(\*\*.*?\*\*)/g).map((segment, segmentIndex) => {
                    const isBold = segment.startsWith('**') && segment.endsWith('**');
                    if (isBold) {
                      const boldContent = segment.slice(2, -2);
                      return (
                        <Text
                          key={`bold-${lineIndex}-${segmentIndex}`}
                          style={{ fontWeight: 'bold' }}
                          selectable={true}
                        >
                          {boldContent}
                        </Text>
                      );
                    }
                    return segment;
                  })}
                </Text>
              ))}
            </View>
          ))}
        </View>
      );
    }

    // For regular messages without metadata
    // Split by paragraphs (double newlines)
    const paragraphs = messageContent.split(/\n\n+/);

    return (
      <View style={{width: '100%'}}>
        {paragraphs.map((paragraph, paragraphIndex) => (
          <View
            key={`paragraph-${paragraphIndex}`}
            style={{
              marginBottom: paragraphIndex < paragraphs.length - 1 ? 24 : 0 // More space between paragraphs, no space after last
            }}
          >
            {paragraph.split('\n').map((line, lineIndex) => (
              <Text
                key={`line-${paragraphIndex}-${lineIndex}`}
                style={[
                  isUser 
                    ? styles.userMessageText 
                    : isError 
                      ? styles.errorText 
                      : styles.assistanceMessageText,
                  { marginBottom: lineIndex < paragraph.split('\n').length - 1 ? 8 : 0 } // Space between lines, no space after last
                ]}
                selectable={true}
                onLongPress={() => copyToClipboard(line)}
              >
                {line.split(/(\*\*.*?\*\*)/g).map((segment, segmentIndex) => {
                  const isBold = segment.startsWith('**') && segment.endsWith('**');
                  if (isBold) {
                    const boldContent = segment.slice(2, -2);
                    return (
                      <Text
                        key={`bold-${lineIndex}-${segmentIndex}`}
                        style={{ fontWeight: 'bold' }}
                        selectable={true}
                      >
                        {boldContent}
                      </Text>
                    );
                  }
                  return segment;
                })}
              </Text>
            ))}
          </View>
        ))}
      </View>
    );
  };

  // This function is used in the context menu to copy the entire message
  const copyEntireMessage = () => {
    copyToClipboard(messageContent);
  };

  return (
    <View>
      {!isUser && (
        <View style={styles.assistantIcon}>
          <Avatar.Icon 
            size={24} 
            icon={isError ? "alert-circle" : "robot"} 
            style={[styles.avatar, isError && { backgroundColor: '#FFCDD2' }]}
            color={isError ? '#D32F2F' : undefined}
          />
          <Text style={isError ? [styles.assistanceText, styles.errorIcon] : styles.assistanceText}>
            {isError ? "Error" : "Assistant"}
          </Text>
          {isLoading && (
            <ActivityIndicator
              style={styles.loading}
              size={20}
              color={theme.colors.primary}
            />
          )}
        </View>
      )}
      <View
        style={[
          styles.messageContainer,
          isUser 
            ? styles.userMessage 
            : isError 
              ? styles.errorMessage 
              : styles.assistantMessage
        ]}
        onLongPress={(event) => showContextMenu(event)}
      >
        {renderContent()}
        
        {isError && onRetry && (
          <View style={styles.retryContainer}>
            <TouchableOpacity 
              style={styles.retryButton}
              onPress={() => onRetry(item)}
            >
              <Text style={styles.retryButtonText}>Try Again</Text>
            </TouchableOpacity>
          </View>
        )}
      </View>

      {/* Context Menu for Android */}
      <Menu
        visible={menuVisible}
        onDismiss={() => setMenuVisible(false)}
        anchor={menuPosition}
      >
        <Menu.Item
          onPress={() => {
            copyEntireMessage();
            setMenuVisible(false);
          }}
          title="Copy"
        />
        <Menu.Item
          onPress={handleDelete}
          title="Delete"
        />
      </Menu>
    </View>
  );
};

export default RenderMessage;
