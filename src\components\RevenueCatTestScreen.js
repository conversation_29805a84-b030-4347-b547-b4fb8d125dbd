import React, { useState, useEffect } from 'react';
import AdmobTestScreen from './AdmobTestScreen';
import {
  View,
  ScrollView,
  StyleSheet,
  Alert,
  Platform,
} from 'react-native';
import {
  Button,
  Card,
  Text,
  Surface,
  Chip,
  ActivityIndicator,
  Divider,
  useTheme,
} from 'react-native-paper';
import { REVENUECAT_API_KEY, REVENUECAT_PROJECT_ID, REVENUECAT_REST_API_KEY } from '@env';
import RevenueCatService from '../services/RevenueCatService';
import Purchases from 'react-native-purchases';

const RevenueCatTestScreen = () => {
  const theme = useTheme();
  const [isLoading, setIsLoading] = useState(false);
  const [isInitialized, setIsInitialized] = useState(false);
  const [logs, setLogs] = useState([]);
  const [offerings, setOfferings] = useState(null);
  const [customerInfo, setCustomerInfo] = useState(null);
  const [diagnosticInfo, setDiagnosticInfo] = useState({});

  useEffect(() => {
    updateDiagnosticInfo();
  }, []);

  const addLog = (message, type = 'info') => {
    const timestamp = new Date().toLocaleTimeString();
    const logEntry = {
      id: `${Date.now()}_${Math.random()}`, // Make unique key
      timestamp,
      message,
      type
    };
    setLogs(prev => [...prev, logEntry]);
    //console.log(`[RevenueCatTest] ${message}`);
  };

  const updateDiagnosticInfo = () => {
    const info = {
      sdkAvailable: RevenueCatService.isAvailable(),
      apiKeyConfigured: !!REVENUECAT_API_KEY,
      apiKeyValue: REVENUECAT_API_KEY ? `${REVENUECAT_API_KEY.substring(0, 8)}...` : 'Missing',
      restApiKeyConfigured: !!REVENUECAT_REST_API_KEY,
      restApiKeyValue: REVENUECAT_REST_API_KEY ? `${REVENUECAT_REST_API_KEY.substring(0, 8)}...` : 'Missing',
      projectIdConfigured: !!REVENUECAT_PROJECT_ID,
      projectIdValue: REVENUECAT_PROJECT_ID || 'Missing',
      platform: Platform.OS,
      isInitialized,
      offeringsLoaded: !!offerings,
      customerInfoLoaded: !!customerInfo,
      ...RevenueCatService.getDiagnosticInfo()
    };
    setDiagnosticInfo(info);
    addLog(`Diagnostic info updated: ${JSON.stringify(info, null, 2)}`);
  };

  // Test Functions
  const testInitialization = async () => {
    setIsLoading(true);
    addLog('=== TESTING REVENUECAT INITIALIZATION ===');

    // First, run detailed diagnostics
    addLog(`SDK Available: ${RevenueCatService.isAvailable()}`);
    addLog(`API Key: ${REVENUECAT_API_KEY ? 'Configured' : 'Missing'}`);
    if (REVENUECAT_API_KEY) {
      addLog(`API Key Value: ${REVENUECAT_API_KEY}`);
    }

    if (!RevenueCatService.isAvailable()) {
      addLog('RevenueCat SDK not available', 'error');
      setIsLoading(false);
      return;
    }

    if (!REVENUECAT_API_KEY) {
      addLog('No API Key provided - this will fail', 'error');
      Alert.alert('Missing API Key', 'Please add REVENUECAT_API_KEY to your .env.dev file');
      setIsLoading(false);
      return;
    }

    try {
      addLog(`Initializing with API Key: ${REVENUECAT_API_KEY}`);
      await RevenueCatService.initialize('test_user_123');
      setIsInitialized(true);
      addLog('RevenueCat initialized successfully!', 'success');
      updateDiagnosticInfo();

      Alert.alert('Success', 'RevenueCat has been initialized successfully!');
    } catch (error) {
      addLog(`Initialization failed: ${error.message}`, 'error');
      Alert.alert('Initialization Failed', error.message);
    } finally {
      setIsLoading(false);
    }
  };

  const testGetOfferings = async () => {
    if (!isInitialized) {
      addLog('RevenueCat not initialized - please initialize first', 'warning');
      return;
    }

    setIsLoading(true);
    addLog('1=== FETCHING OFFERINGS ===');

    try {
      const offeringsResult = await RevenueCatService.getOfferings();
      setOfferings(offeringsResult);

      // Use the new displayPackages method for better formatting
      const displayInfo = RevenueCatService.displayPackages(offeringsResult);

      addLog(`Offerings fetch completed`, 'success');
      addLog(`Total offerings configured: ${displayInfo.debugInfo.totalOfferings}`);
      addLog(`Has current offering: ${displayInfo.debugInfo.hasCurrentOffering}`);

      if (displayInfo.hasOfferings) {
        addLog(`✅ Current offering: ${displayInfo.currentOffering.identifier}`, 'success');
        addLog(`✅ Available packages: ${displayInfo.packages.length}`);

        // Show only the first package
        if (displayInfo.packages.length > 0) {
          const pkg = displayInfo.packages[0];
          addLog(`📦 First Package: ${pkg.identifier}`);
          addLog(JSON.stringify(pkg, null, 2));
          /* addLog(`   Type: ${pkg.packageType}`);
          addLog(`   Product: ${pkg.product.identifier}`);
          addLog(`   Price: ${pkg.product.priceString || 'N/A'}`);
          addLog(`   Currency: ${pkg.product.currencyCode || 'N/A'}`); */
        }

        Alert.alert('Success', `Found ${displayInfo.packages.length} packages in current offering!`);
      } else {
        addLog('⚠️ No current offering or packages found', 'warning');
        addLog('This indicates one of the following issues:');
        addLog('1. No products configured in RevenueCat dashboard');
        addLog('2. Products not set up in App Store Connect/Google Play');
        addLog('3. API key or bundle ID configuration mismatch');
        addLog('4. Products not yet approved/available in store');

        if (displayInfo.allOfferings.length > 0) {
          addLog(`Available offerings (${displayInfo.allOfferings.length}):`);
          displayInfo.allOfferings.forEach(offering => {
            addLog(`- ${offering.identifier} (${offering.packagesCount} packages)`);
          });
        }

        Alert.alert('No Products Found', 'No products are currently available. This is normal for test environments without configured products.');
      }

      // Log raw data for debugging
      //addLog('=== RAW OFFERINGS DATA ===');
      //addLog(JSON.stringify(offeringsResult, null, 2));

      updateDiagnosticInfo();
    } catch (error) {
      addLog(`❌ Failed to fetch offerings: ${error.message}`, 'error');

      // Enhanced error handling
      if (error.message.includes('configuration')) {
        addLog('Configuration error detected. Please verify:', 'error');
        addLog('1. API key matches your RevenueCat project');
        addLog('2. App bundle ID matches project settings');
        addLog('3. Products are configured in dashboard');
        addLog('4. Store products exist and are approved');

        Alert.alert('Configuration Error', 'There is an issue with your RevenueCat configuration. Check the logs for details.');
      } else {
        Alert.alert('Fetch Failed', error.message);
      }
    } finally {
      setIsLoading(false);
    }
  };

  const testGetCustomerInfo = async () => {
    if (!isInitialized) {
      addLog('RevenueCat not initialized - please initialize first', 'warning');
      return;
    }

    setIsLoading(true);
    addLog('Fetching customer info...');

    try {
      const customerInfoResult = await RevenueCatService.getCustomerInfo();
      setCustomerInfo(customerInfoResult);

      addLog('Customer info fetched successfully', 'success');
      addLog(`Customer ID: ${customerInfoResult.originalAppUserId}`);

      const activeEntitlements = Object.keys(customerInfoResult.entitlements.active);
      addLog(`Active entitlements: ${activeEntitlements.length}`);

      if (activeEntitlements.length > 0) {
        activeEntitlements.forEach(entitlement => {
          addLog(`- ${entitlement}: Active`);
        });
      }

      addLog(`Customer info data: ${JSON.stringify(customerInfoResult, null, 2)}`);
      updateDiagnosticInfo();
    } catch (error) {
      addLog(`Failed to fetch customer info: ${error.message}`, 'error');
      Alert.alert('Fetch Failed', error.message);
    } finally {
      setIsLoading(false);
    }
  };

  const testPurchase = async () => {
    if (!isInitialized) {
      addLog('RevenueCat not initialized - please initialize first', 'warning');
      return;
    }

    if (!offerings?.current?.availablePackages?.length) {
      addLog('No packages available - please fetch offerings first', 'warning');
      return;
    }

    setIsLoading(true);
    addLog('Testing purchase...');

    try {
      const packageToPurchase = offerings.current.availablePackages[0];
      addLog(`Attempting to purchase: ${packageToPurchase.identifier}`);

      const result = await RevenueCatService.makePurchase(packageToPurchase);

      if (result.success) {
        addLog('Purchase successful!', 'success');
        addLog(`Product: ${result.productIdentifier}`);

        const entitlements = Object.keys(result.entitlements);
        addLog(`Active entitlements: ${entitlements.join(', ')}`);

        Alert.alert('Purchase Success', 'Purchase completed successfully!');

        // Refresh customer info
        await testGetCustomerInfo();
      } else if (result.cancelled) {
        addLog('Purchase was cancelled by user', 'warning');
      } else {
        addLog(`Purchase failed: ${result.error}`, 'error');
        Alert.alert('Purchase Failed', result.error);
      }
    } catch (error) {
      addLog(`Purchase error: ${error.message}`, 'error');
      Alert.alert('Purchase Error', error.message);
    } finally {
      setIsLoading(false);
    }
  };

  const testRestorePurchases = async () => {
    if (!isInitialized) {
      addLog('RevenueCat not initialized - please initialize first', 'warning');
      return;
    }

    setIsLoading(true);
    addLog('Restoring purchases...');

    try {
      const customerInfoResult = await RevenueCatService.restorePurchases();

      addLog('Purchases restored successfully!', 'success');

      const activeEntitlements = Object.keys(customerInfoResult.entitlements.active);
      addLog(`Active entitlements after restore: ${activeEntitlements.length}`);

      if (activeEntitlements.length > 0) {
        Alert.alert('Restore Success', 'Purchases have been restored successfully!');
      } else {
        Alert.alert('No Purchases', 'No previous purchases found to restore.');
      }

      // Refresh customer info
      setCustomerInfo(customerInfoResult);
      updateDiagnosticInfo();
    } catch (error) {
      addLog(`Restore failed: ${error.message}`, 'error');
      Alert.alert('Restore Failed', error.message);
    } finally {
      setIsLoading(false);
    }
  };

  const testChangePlan = async () => {
    if (!isInitialized) {
      addLog('RevenueCat not initialized - please initialize first', 'warning');
      return;
    }

    // Debug Purchases object
    addLog(`Purchases object available: ${!!Purchases}`);
    if (Purchases) {
      addLog(`Purchases.PRORATION_MODE.DEFERRED: ${Purchases.PRORATION_MODE.DEFERRED}`);
    }

    if (!offerings?.current?.availablePackages?.length || offerings.current.availablePackages.length < 2) {
      addLog('Not enough packages available to test plan change', 'warning');
      Alert.alert('Error', 'At least 2 packages are required to test plan change');
      return;
    }

    setIsLoading(true);
    addLog('=== TESTING PLAN CHANGE ===');

    try {
      const packages = offerings.current.availablePackages;
      const oldPackage = packages[0];
      const newPackage = packages[1];

      addLog(`Current package: ${oldPackage.identifier}`);
      addLog(`New package: ${newPackage.identifier}`);

      const upgradeInfo = {
        oldProductIdentifier: oldPackage.identifier,
        prorationMode: Purchases.PRORATION_MODE.DEFERRED
      };

      addLog(`Attempting to change plan with upgrade info: ${JSON.stringify(upgradeInfo)}`);

      const result = await RevenueCatService.makePurchase(newPackage, upgradeInfo);

      if (result.success) {
        addLog('Plan change successful!', 'success');
        addLog(`New product: ${result.productIdentifier}`);
        Alert.alert('Success', 'Plan changed successfully!');

        // Refresh customer info
        await testGetCustomerInfo();
      } else if (result.cancelled) {
        addLog('Plan change was cancelled by user', 'warning');
      } else {
        addLog(`Plan change failed: ${result.error}`, 'error');
        Alert.alert('Error', result.error);
      }
    } catch (error) {
      addLog(`Plan change error: ${error.message}`, 'error');
      Alert.alert('Error', error.message);
    } finally {
      setIsLoading(false);
    }
  };

  /**
* Tests changing a user's subscription to a different plan that will take effect at next renewal
* @param newPackageIdentifier - The identifier of the new subscription package to switch to
*/
  const testChangeSubscription = async (newPackageIdentifier) => {
    // First check if RevenueCat has been initialized
    if (!isInitialized) {
      addLog('RevenueCat not initialized - please initialize first', 'warning');
      return; // Exit the function if not initialized
    }



    // Set loading state to true to show activity indicator
    setIsLoading(true);
    addLog('Starting subscription change process...');



    try {
      // STEP 1: Verify the user has an active subscription to change
      // Get the current customer info from RevenueCat
      const currentInfo = await RevenueCatService.getCustomerInfo();



      // Extract all active subscriptions from the customer info
      // Note: A user might have multiple active entitlements in some cases
      const activeSubscriptions = Object.values(currentInfo.entitlements.active);



      /* // If no active subscriptions found, show error and exit
      if (activeSubscriptions.length === 0) {
        addLog('No active subscription found - user must have an active sub to change', 'error');
        Alert.alert('Error', 'No active subscription found to change');
        return;
      } */



      // STEP 2: Find the new package the user wants to switch to
      // Get current offerings from RevenueCat
      const offerings = await RevenueCatService.getOfferings();

      console.log(offerings);

      // Search through available packages to find the one matching our identifier
      const newPackage = offerings.current?.availablePackages.find(
        pkg => pkg.identifier === newPackageIdentifier
      );



      // If the requested package isn't available, show error
      if (!newPackage) {
        addLog('Package "${newPackageIdentifier}" not found in offerings', 'error');
        Alert.alert('Error', 'Requested subscription package not available');
        return;
      }



      // STEP 3: Prepare the subscription change parameters
      // Platform-specific configuration for subscription changes:
      const purchaseOptions = {
        // Android-specific parameters:
        googleProductChangeInfo: {
          // The product ID of the current subscription we're changing from
          oldProductIdentifier: activeSubscriptions[0].productIdentifier,
          // Important: DEFERRED means change happens at next renewal
          prorationMode: Purchases.PRORATION_MODE.DEFERRED,
        },
        // iOS-specific parameters (older format but still supported):
        upgradeInfo: {
          // The SKU of the current subscription (same as productIdentifier)
          oldSKU: activeSubscriptions[0].productIdentifier,
          // Same deferred mode for iOS
          prorationMode: Purchases.PRORATION_MODE.DEFERRED,
        },
      };



      // Log what we're about to do for debugging
      addLog('Attempting to change from ${ activeSubscriptions[0].productIdentifier } to ${ newPackage.identifier }', 'info');
      addLog('Using purchase options: ${ JSON.stringify(purchaseOptions) }', 'debug');



      // STEP 4: Execute the subscription change
      // Make the purchase with our special change parameters
      const result = await RevenueCatService.makePurchase(newPackage, purchaseOptions);



      // Handle the result
      if (result.success) {
        // Success case - change will happen at next renewal
        addLog('Subscription change processed successfully! Change will occur at next renewal.', 'success');
        Alert.alert('Success', 'Your subscription will be updated at your next billing date');



        // Good practice: Refresh customer info to see the pending change
        const updatedInfo = await RevenueCatService.getCustomerInfo();
        addLog('Updated customer info: ${ JSON.stringify(updatedInfo) }', 'debug');
      } else {
        // Handle failure cases
        if (result.cancelled) {
          addLog('User cancelled the subscription change', 'warning');
        } else {
          addLog('Subscription change failed: ${ result.error }', 'error');
          Alert.alert('Error', result.error || 'Unknown error during subscription change');
        }
      }
    } catch (error) {
      // Handle any unexpected errors
      addLog('Unexpected error during subscription change: ${ error.message }', 'error');
      console.error('Full error details:', error);



      // Show user-friendly error message
      Alert.alert('Error', 'An unexpected error occurred while changing your subscription. Please try again.');
    } finally {
      // Always turn off loading indicator when done
      setIsLoading(false);
      addLog('Subscription change process completed', 'info');
    }
  };

  const testExamSpecificPlans = async () => {
    if (!isInitialized) {
      Alert.alert('Error', 'Please initialize RevenueCat first');
      return;
    }

    setIsLoading(true);
    addLog('=== TESTING EXAM-SPECIFIC PLANS ===');

    try {
      // Test with known exam codes from offerings.txt
      const testExamCodes = ['sap_c02', 'scs_c02', 'aif_c01', 'soa_c02'];

      const offeringsResult = await RevenueCatService.getOfferings();
      addLog(`Offerings fetched successfully`, 'success');

      for (const examCode of testExamCodes) {
        addLog(`\n--- Testing exam code: ${examCode.toUpperCase()} ---`);

        if (!offeringsResult?.current?.availablePackages?.length) {
          addLog('No packages available in offerings', 'warning');
          continue;
        }

        const packages = offeringsResult.current.availablePackages;
        const examSpecificPackages = packages.filter(pkg => {
          const identifier = pkg.identifier?.toLowerCase() || '';
          return identifier.startsWith(`${examCode.toLowerCase()}:`);
        });

        addLog(`Found ${examSpecificPackages.length} packages for ${examCode}`);

        if (examSpecificPackages.length > 0) {
          examSpecificPackages.forEach(pkg => {
            const parts = pkg.identifier.split(':');
            const planType = parts[1];
            const price = pkg.product?.priceString || 'N/A';
            const currency = pkg.product?.currencyCode || 'N/A';
            addLog(`  ✅ ${planType}: ${price} ${currency}`, 'success');
          });
        } else {
          addLog(`  ❌ No plans found for ${examCode}`, 'warning');
        }
      }

    } catch (error) {
      addLog(`❌ Test failed: ${error.message}`, 'error');
      Alert.alert('Test Failed', error.message);
    } finally {
      setIsLoading(false);
    }
  };

  const testGetProducts = async () => {
    setIsLoading(true);
    addLog('=== FETCHING PRODUCTS VIA REST API ===');
    addLog(`Using project ID: ${REVENUECAT_PROJECT_ID || 'Not set'}`);

    try {
      // Fetch products with a limit of 50 to get more results
      const productsResult = await RevenueCatService.getProductsViaRestApi({ limit: 50 });

      addLog(`Products fetch completed via REST API`, 'success');
      addLog(`Total products found: ${productsResult.count}`);

      if (productsResult.count > 0) {
        addLog(`✅ Found ${productsResult.count} products`, 'success');

        // Display products from the REST API format
        /* productsResult.products.forEach((product, index) => {
          addLog(`📦 Product ${index + 1}: ${product.id}`);
          addLog(`   Store Identifier: ${product.store_identifier || 'N/A'}`);
          addLog(`   Type: ${product.type || 'N/A'}`);
          addLog(`   Display Name: ${product.display_name || 'N/A'}`);
          
          // Check if it's a subscription product
          if (product.subscription) {
            addLog(`   Subscription Details:`);
            addLog(`     Duration: ${product.subscription.duration || 'N/A'}`);
            addLog(`     Trial Duration: ${product.subscription.trial_duration || 'None'}`);
            addLog(`     Grace Period: ${product.subscription.grace_period_duration || 'None'}`);
          }
          
          // Check if it's a one-time product
          if (product.one_time) {
            addLog(`   One-Time Purchase`);
          }
          
          addLog(`   Created At: ${new Date(product.created_at).toLocaleString()}`);
          addLog(`   App ID: ${product.app_id}`);
        }); */

        // Show pagination info if available
        if (productsResult.nextPage) {
          addLog(`More products available. Next page: ${productsResult.nextPage}`);
        }

        Alert.alert('Success', `Found ${productsResult.count} products!`);
      } else {
        addLog('⚠️ No products found', 'warning');
        addLog('This indicates one of the following issues:');
        addLog('1. No products configured in RevenueCat dashboard');
        addLog('2. API key does not have sufficient permissions');
        addLog('3. Project ID is incorrect');

        Alert.alert('No Products Found', 'No products are currently available in your RevenueCat project.');
      }

      // Log raw data for debugging (limited to avoid overwhelming the logs)
      addLog('=== API RESPONSE INFO ===');
      addLog(`URL: ${productsResult.url || 'N/A'}`);
      addLog(`Next Page: ${productsResult.nextPage || 'None'}`);

      // Log first product as an example if available
      if (productsResult.count > 0) {
        addLog('=== SAMPLE PRODUCT DATA ===');
        addLog(JSON.stringify(productsResult.products[0], null, 2));
      }

      updateDiagnosticInfo();
    } catch (error) {
      addLog(`❌ Failed to fetch products: ${error.message}`, 'error');

      // Add more specific error handling
      if (error.message.includes('not defined in environment')) {
        addLog('⚠️ Project ID is missing in environment variables', 'error');
        addLog('Make sure REVENUECAT_PROJECT_ID is set in .env.dev file');
      } else if (error.message.includes('API request failed')) {
        addLog('⚠️ API request failed - check credentials and permissions', 'error');
        addLog('Make sure your API key has the correct permissions for reading products');
      }

      Alert.alert('Fetch Failed', error.message);
    } finally {
      setIsLoading(false);
    }
  };

  const clearLogs = () => {
    setLogs([]);
    addLog('Logs cleared');
  };

  const runFullDiagnostics = async () => {
    setIsLoading(true);
    addLog('=== RUNNING FULL REVENUECAT DIAGNOSTICS ===');

    // 1. Basic checks
    addLog('--- Basic Checks ---');
    addLog(`Platform: ${Platform.OS}`);
    addLog(`SDK Available: ${RevenueCatService.isAvailable()}`);
    addLog(`API Key Configured: ${!!REVENUECAT_API_KEY}`);

    // 2. Environment variable debugging
    addLog('--- Environment Variable Debug ---');
    addLog(`Raw REVENUECAT_API_KEY: "${REVENUECAT_API_KEY}"`);
    addLog(`Type of REVENUECAT_API_KEY: ${typeof REVENUECAT_API_KEY}`);
    addLog(`Length of REVENUECAT_API_KEY: ${REVENUECAT_API_KEY ? REVENUECAT_API_KEY.length : 'undefined'}`);
    addLog(`Boolean check: ${!!REVENUECAT_API_KEY}`);

    // 3. Try to get diagnostic info
    addLog('--- SDK Diagnostics ---');
    const diagnostics = RevenueCatService.getDiagnosticInfo();
    addLog(`Diagnostic info: ${JSON.stringify(diagnostics, null, 2)}`);

    addLog('=== DIAGNOSTICS COMPLETE ===');
    setIsLoading(false);
  };

  const getLogColor = (type) => {
    switch (type) {
      case 'success': return theme.colors.primary;
      case 'error': return theme.colors.error;
      case 'warning': return theme.colors.tertiary;
      default: return theme.colors.onSurface;
    }
  };

  return (
    <ScrollView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      {/* Header */}
      <Card style={styles.card}>
        <Card.Title title="RevenueCat SDK Test" />
        <Card.Content>
          <Text variant="bodyMedium" style={{ color: theme.colors.onSurfaceVariant }}>
            Test RevenueCat SDK integration and functionality
          </Text>
        </Card.Content>
      </Card>

      {/* Status Indicators */}
      <Card style={styles.card}>
        <Card.Title title="Status" />
        <Card.Content>
          <View style={styles.statusContainer}>
            <Chip
              icon={RevenueCatService.isAvailable() ? "check" : "close"}
              style={{ backgroundColor: RevenueCatService.isAvailable() ? theme.colors.primaryContainer : theme.colors.errorContainer }}
            >
              SDK Available
            </Chip>
            <Chip
              icon={!!REVENUECAT_API_KEY ? "check" : "close"}
              style={{ backgroundColor: !!REVENUECAT_API_KEY ? theme.colors.primaryContainer : theme.colors.errorContainer }}
            >
              API Key
            </Chip>
            <Chip
              icon={isInitialized ? "check" : "close"}
              style={{ backgroundColor: isInitialized ? theme.colors.primaryContainer : theme.colors.errorContainer }}
            >
              Initialized
            </Chip>
          </View>
        </Card.Content>
      </Card>

      {/* Test Actions */}
      <Card style={styles.card}>
        <Card.Title title="Test Actions" />
        <Card.Content>
          <View style={styles.buttonContainer}>

            <Button
              mode="outlined"
              onPress={testGetProducts}
              disabled={isLoading}
              style={styles.button}
            >
              Get Products via Rest API v2
            </Button>

            <Button
              mode="contained"
              onPress={testInitialization}
              disabled={isLoading}
              style={styles.button}
            >
              Initialize RevenueCat
            </Button>

            <Button
              mode="outlined"
              onPress={testGetOfferings}
              disabled={isLoading || !isInitialized}
              style={styles.button}
            >
              Get Offerings
            </Button>

            <Button
              mode="outlined"
              onPress={testExamSpecificPlans}
              disabled={isLoading || !isInitialized}
              style={styles.button}
            >
              Test Exam-Specific Plans
            </Button>

            <Button
              mode="outlined"
              onPress={testGetCustomerInfo}
              disabled={isLoading || !isInitialized}
              style={styles.button}
            >
              Get Customer Info
            </Button>

            <Button
              mode="outlined"
              onPress={testPurchase}
              disabled={isLoading || !isInitialized}
              style={styles.button}
            >
              Test Purchase
            </Button>

            <Button
              mode="outlined"
              onPress={testRestorePurchases}
              disabled={isLoading || !isInitialized}
              style={styles.button}
            >
              Restore Purchases
            </Button>

            <Button
              mode="outlined"
              onPress={testChangeSubscription}
              style={styles.button}
            >
              Test Change Subscription
            </Button>

            <Button
              mode="outlined"
              onPress={testChangePlan}
              disabled={isLoading || !isInitialized || !offerings?.current?.availablePackages?.length}
              style={styles.button}
            >
              Test Change Plan
            </Button>

            <Button
              mode="outlined"
              onPress={runFullDiagnostics}
              disabled={isLoading}
              style={styles.button}
            >
              Run Diagnostics
            </Button>

            <Button
              mode="text"
              onPress={clearLogs}
              disabled={isLoading}
              style={styles.button}
            >
              Clear Logs
            </Button>

          </View>
        </Card.Content>
      </Card>

      {/* Loading Indicator */}
      {isLoading && (
        <Card style={styles.card}>
          <Card.Content>
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="large" color={theme.colors.primary} />
              <Text style={{ marginTop: 16, color: theme.colors.onSurface }}>
                Processing...
              </Text>
            </View>
          </Card.Content>
        </Card>
      )}

      {/* Debug Information */}
      <Card style={styles.card}>
        <Card.Title title="Debug Information" />
        <Card.Content>
          <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant }}>
            Platform: {Platform.OS}
          </Text>
          <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant }}>
            SDK Available: {RevenueCatService.isAvailable() ? 'Yes' : 'No'}
          </Text>
          <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant }}>
            SDK API Key: {REVENUECAT_API_KEY ? 'Configured' : 'Missing'}
          </Text>
          <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant }}>
            REST API Key: {REVENUECAT_REST_API_KEY ? 'Configured' : 'Missing'}
          </Text>
          <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant }}>
            Project ID: {REVENUECAT_PROJECT_ID ? 'Configured' : 'Missing'}
          </Text>
          <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant }}>
            Initialized: {isInitialized ? 'Yes' : 'No'}
          </Text>
        </Card.Content>
      </Card>

      {/* Logs */}
      <Card style={styles.card}>
        <Card.Title title="Logs" />
        <Card.Content>
          <ScrollView style={styles.logsContainer} nestedScrollEnabled>
            {logs.map((log) => (
              <View key={log.id} style={styles.logEntry}>
                <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant }}>
                  {log.timestamp}
                </Text>
                <Text
                  variant="bodySmall"
                  style={{ color: getLogColor(log.type), marginTop: 2 }}
                >
                  {log.message}
                </Text>
                <Divider style={{ marginTop: 8 }} />
              </View>
            ))}
          </ScrollView>
        </Card.Content>
      </Card>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  card: {
    marginBottom: 16,
  },
  statusContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  buttonContainer: {
    gap: 12,
  },
  button: {
    marginVertical: 4,
  },
  loadingContainer: {
    alignItems: 'center',
    padding: 20,
  },
  logsContainer: {
    maxHeight: 300,
  },
  logEntry: {
    marginBottom: 8,
  },
});

export default RevenueCatTestScreen;