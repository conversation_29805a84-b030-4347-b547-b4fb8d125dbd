import { Alert, StyleSheet, View, Pressable, ScrollView, ActivityIndicator } from 'react-native';
import React, { useCallback, useState, useEffect } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {
  Button,
  IconButton,
  Modal,
  Portal,
  RadioButton,
  Text,
  useTheme,
  Surface,
} from 'react-native-paper';

import { usePurchase } from '../store/PurchaseContext';
import { useAICredit } from '../store/AICreditContext';
import { useQnAContext } from '../store/QnAContext';
import { useLogin } from '../store/LoginContext';
import { useNavigation } from '@react-navigation/native';
import LoginModal from './LoginModal';
import RevenueCatService from '../services/RevenueCatService';
import { useExamContext } from '../store/ExamContext';
import FixedFontButton from '../components/FixedFontButton';

const BuyNowModal = ({
  buyNowModalVisible,
  setBuyNowModalVisible,
  selectedProduct,
  onPurchaseComplete,
}) => {
  const { colors } = useTheme();
  const navigation = useNavigation();
  const { selectExam, selectedExam } = useExamContext();
  const [selectedPlan, setSelectedPlan] = useState(''); // Will be set when plans are loaded
  const [processingPurchase, setProcessingPurchase] = useState(false);
  const [purchaseCompleted, setPurchaseCompleted] = useState(false);
  const [loginModalVisible, setLoginModalVisible] = useState(false);

  // RevenueCat state
  const [revenueCatInitialized, setRevenueCatInitialized] = useState(false);
  const [revenueCatLoading, setRevenueCatLoading] = useState(false);
  const [revenueCatError, setRevenueCatError] = useState(null);
  const [dynamicPlans, setDynamicPlans] = useState([]);
  const [activeSubscription, setActiveSubscription] = useState(null);

  // Get purchase, AI credit, QnA, and login contexts
  const purchaseContext = usePurchase();
  //const aiCreditContext = useAICredit();
  const { addCredits } = useAICredit();
  const { loadQnA } = useQnAContext();
  const loginContext = useLogin();
  const { isLoggedIn } = loginContext;

  // Extract all the functions and data we need from the purchase context
  const {
    updateUser,
    storeSubscriptionStatus,
    //isSubscriptionActive,
    purchases,    // Array of user purchases
    subscriptionInfo // User's subscription information
  } = purchaseContext || {};

  // Log selected product details if available
  if (selectedProduct) {
    if (typeof selectedProduct === 'object') {
      /* console.log('Selected Product Details:', {
        id: selectedProduct.id,
        exam_name: selectedProduct.exam_name,
        exam_code: selectedProduct.exam_code,
        total_questions: selectedProduct.total_questions
      }); */
    } else if (typeof selectedProduct === 'string') {
      console.log('Selected Product is a string (likely exam_code):', selectedProduct);
    } else {
      console.log('Selected Product is of unexpected type:', typeof selectedProduct);
    }
  }

  // Check if user is logged in and context is initialized
  useEffect(() => {
    if (loginContext.isInitialized) {
      console.log('[BuyNowModal] Login context initialized');
      
      // If user is signed in but purchase context is not available, log a warning
      if (isLoggedIn && !purchaseContext) {
        console.warn('[BuyNowModal] User is signed in but purchase context is not available');
      }
    }
  }, [isLoggedIn, loginContext.isInitialized]);

  // Reset selected plan when modal opens to ensure auto-selection works


  // Initialize RevenueCat when modal opens
  useEffect(() => {
    const initialize = async () => {
      if (!buyNowModalVisible) return;

      try {
        setRevenueCatLoading(true);
        setRevenueCatError(null);

        console.log('[BuyNowModal] Initializing RevenueCat...');

        // Get user ID for RevenueCat initialization
        let userId = 'anonymous_user';
        try {
          if (loginContext && loginContext.user && loginContext.user.id) {
            userId = loginContext.user.id;
            console.log('[BuyNowModal] Using Google ID for RevenueCat:', userId);
          } else {
            // Fallback to AsyncStorage
            const userJson = await AsyncStorage.getItem('user');
            if (userJson) {
              const userData = JSON.parse(userJson);
              if (userData && userData.id) {
                userId = userData.id;
                console.log('[BuyNowModal] Using stored Google ID for RevenueCat:', userId);
              }
            }
          }
        } catch (error) {
          console.warn('[BuyNowModal] Could not get user ID, using anonymous:', error);
        }

        // Initialize RevenueCat with user ID
        await RevenueCatService.initialize(userId);
        setRevenueCatInitialized(true);
        console.log('[BuyNowModal] RevenueCat initialized successfully with user ID:', userId);

        // Fetch offerings
        console.log('[BuyNowModal] Fetching RevenueCat offerings...');
        const offerings = await RevenueCatService.getOfferings();

        // Get exam code from selectedProduct
        let examCode = null;
        if (selectedProduct) {
          if (typeof selectedProduct === 'object' && selectedProduct.exam_code) {
            examCode = selectedProduct.exam_code;
          } else if (typeof selectedProduct === 'string') {
            examCode = selectedProduct;
          }
        }

        console.log('[BuyNowModal] Using exam code for plan filtering:', examCode);

        // Process offerings into exam-specific dynamic plans
        const processedPlans = RevenueCatService.getExamSpecificPlans(offerings, examCode);
        setDynamicPlans(processedPlans);

        console.log('[BuyNowModal] RevenueCat exam-specific plans processed:', processedPlans);
        console.log('[BuyNowModal] Available plan types:', processedPlans.map(p => `${p.id} (${p.price})`));

        // Log popular plan for auto-selection
        const popularPlan = processedPlans.find(p => p.isPopular);
        if (popularPlan) {
          console.log('[BuyNowModal] 🌟 Popular plan identified for auto-selection:', popularPlan.name, '(' + popularPlan.id + ')');
        } else {
          console.log('[BuyNowModal] ⚠️ No popular plan found in processed plans');
        }

      } catch (error) {
        console.error('[BuyNowModal] RevenueCat initialization failed:', error);
        setRevenueCatError(error.message);

        // No fallback to static plans - show "No plans available" instead
        setDynamicPlans([]);
        console.log('[BuyNowModal] RevenueCat failed - no plans will be shown');
      } finally {
        setRevenueCatLoading(false);
      }
    };

    initialize();
  }, [buyNowModalVisible]);

  // Set default selected plan when dynamic plans are loaded
  useEffect(() => {
    if (dynamicPlans.length > 0) {
        // Find the popular plan first, or default to the first plan
        const popularPlan = dynamicPlans.find(p => p.isPopular);
        const defaultPlan = popularPlan || dynamicPlans[0];
        setSelectedPlan(defaultPlan.id);
    }
  }, [dynamicPlans]);

  // Function to match active subscription with available plans
  const getPlansWithCurrentStatus = () => {

    // Early return if no active subscription
    if (!activeSubscription) {
      console.log('No active subscription available');
      return dynamicPlans.map(plan => ({ ...plan, isCurrentPlan: false }));
    }

    // Get customer info from RevenueCat
    const customerInfo = activeSubscription._rawCustomerInfo || {};
    const activeSubscriptions = customerInfo.activeSubscriptions || [];

    console.log('Input state:', {
      dynamicPlansCount: dynamicPlans.length,
      activeSubscriptions: activeSubscriptions.map(s => s),
      purchasesCount: purchases?.length || 0,
      selectedProduct: selectedProduct ? {
        id: selectedProduct.id,
        exam_code: selectedProduct.exam_code
      } : null
    });

    if (!dynamicPlans.length) {
      console.log('No dynamic plans available');
      return [];
    }

    const result = dynamicPlans.map(plan => {
      let isCurrentPlan = false;
      let matchType = 'none';
      let matchDetails = {};
      const planProductId = plan?.productId?.toLowerCase() || '';

      console.log('Plan details:', {
        productId: plan.productId,
        planFrequency: plan.planFrequency,
        examCode: plan.examCode
      });

      // 1. Check against activeSubscriptions array first
      if (activeSubscriptions.length > 0) {
        console.log('Active subscriptions to match against:', activeSubscriptions);

        const exactMatch = activeSubscriptions.some(subId => {
          // Create all possible normalized versions for comparison
          const subVariations = [
            subId.toLowerCase().trim(),
            subId.toLowerCase().trim().replace(/_/g, ':'),
            subId.toLowerCase().trim().replace(/:/g, '_')
          ];

          const planVariations = [
            planProductId.toLowerCase().trim(),
            planProductId.toLowerCase().trim().replace(/_/g, ':'),
            planProductId.toLowerCase().trim().replace(/:/g, '_')
          ];

          // Check all combinations of variations
          const match = subVariations.some(subV =>
            planVariations.some(planV => subV === planV)
          );

          console.log(`Comparing IDs:
            Subscription: "${subId}" (variations: ${JSON.stringify(subVariations)})
            Plan:        "${planProductId}" (variations: ${JSON.stringify(planVariations)})
            Match: ${match}`);

          return match;
        });

        if (exactMatch) {
          isCurrentPlan = true;
          matchType = 'exact';
          matchDetails = { type: 'exact', matchedId: planProductId };
          console.log('✅ Exact match in activeSubscriptions');
        } else {
          // 2. Check base product ID (without frequency suffix)
          const basePlanId = planProductId.replace(/_monthly|_weekly|_bimonthly|_annual|_yearly/g, '');
          console.log('Base plan ID (without frequency):', basePlanId);

          const baseMatch = activeSubscriptions.some(subId => {
            const baseSubId = subId.toLowerCase().replace(/_monthly|_weekly|_bimonthly|_annual|_yearly/g, '');
            const match = baseSubId === basePlanId;
            console.log(`Comparing base IDs "${baseSubId}" with "${basePlanId}": ${match}`);
            return match;
          });

          if (baseMatch) {
            isCurrentPlan = true;
            matchType = 'base';
            matchDetails = { type: 'base', basePlanId };
            console.log('✅ Base product ID match');
          }
        }
      }

      // 3. Fallback to frequency-based matching if no direct match found
      if (!isCurrentPlan && activeSubscription?.productIdentifier) {
        const subProductId = activeSubscription.productIdentifier.toLowerCase();
        console.log('Fallback matching with subscription product ID:', subProductId);

        const frequencyMatches = [
          { pattern: 'weekly', frequency: 'weekly' },
          { pattern: 'monthly', frequency: 'monthly', exclude: 'bimonthly' },
          { pattern: 'bimonthly', frequency: 'bimonthly', aliases: ['2month'] }
        ];

        for (const { pattern, frequency, exclude, aliases } of frequencyMatches) {
          const hasPattern = subProductId.includes(pattern) ||
            (aliases && aliases.some(a => subProductId.includes(a)));
          const isExcluded = exclude && subProductId.includes(exclude);

          console.log(`Checking frequency pattern "${pattern}": hasPattern=${hasPattern}, isExcluded=${isExcluded}, planFrequency=${plan.planFrequency}`);

          if (hasPattern && !isExcluded && plan.planFrequency === frequency) {
            isCurrentPlan = true;
            matchType = 'frequency';
            matchDetails = { type: 'frequency', pattern };
            console.log(`✅ Frequency-based match (${pattern})`);
            break;
          }
        }
      }

      console.log(`Final match result: ${isCurrentPlan ? '✅ CURRENT' : '❌ NOT CURRENT'} (${matchType})`);
      if (isCurrentPlan) {
        console.log('Match details:', matchDetails);
      } else {
        console.log('No matching subscription found for this plan');
      }

      return { ...plan, isCurrentPlan, _matchType: matchType, _matchDetails: matchDetails };
    });

    // Final verification - ensure at least one plan matches active subscription
    const activePlan = result.find(p => p.isCurrentPlan);
    if (activePlan) {
      console.log('✅ Active plan identified:', {
        name: activePlan.name,
        productId: activePlan.productId,
        matchType: activePlan._matchType
      });
    } else if (activeSubscriptions.length > 0) {
      console.warn('⚠️ No matching plan found for active subscriptions:', {
        activeSubscriptions,
        availablePlanIds: dynamicPlans.map(p => p.productId)
      });
    }

    console.log('Final plan status:',
      result.map(p => ({
        name: p.name,
        productId: p.productId,
        isCurrent: p.isCurrentPlan,
        matchType: p._matchType,
        matchDetails: p._matchDetails
      }))
    );

    return result;
  };

  // Handle successful login
  const handleLoginSuccess = async (userData) => {
    setLoginModalVisible(false);
    setProcessingPurchase(true);

    try {
      console.log('[BuyNowModal] Login successful, processing post-login flow');

      // Check if updateUser function is available
      if (typeof updateUser === 'function' && userData && userData.id) {
        console.log('[USER_UPDATE] User ID:', userData.id);

        // Get RevenueCat customer info if available
        let revenueCatCustomerInfo = null;
        try {
          if (revenueCatInitialized) {
            console.log('[USER_UPDATE] Fetching RevenueCat customer info');
            revenueCatCustomerInfo = await RevenueCatService.getCustomerInfo();
            console.log('[USER_UPDATE] RevenueCat data:', {
              activeSubscriptions: revenueCatCustomerInfo?.activeSubscriptions || []
            });
          }
        } catch (error) {
          console.error('[USER_UPDATE] Error fetching RevenueCat info:', error);
        }

        console.log('[USER_UPDATE] Calling updateUser with RevenueCat data');
        await updateUser(userData.id, {
          revenueCatCustomerInfo: revenueCatCustomerInfo
        });

        console.log('[USER_UPDATE] Update completed');
      }

      // Log the selected product details
      console.log('[BuyNowModal] Selected product details:', selectedProduct);

      // Ensure the selectedProduct has a valid ID
      if (!selectedProduct || !selectedProduct.id) {
        console.error('[BuyNowModal] Invalid selected product:', selectedProduct);
        Alert.alert(
          'Error',
          'Invalid product selected. Please try again.',
          [{ text: 'OK' }]
        );
        setProcessingPurchase(false);
        return;
      }

      // Direct check of purchases array for premium access
      if (selectedProduct && selectedProduct.id && Array.isArray(purchases) && purchases.length > 0) {
        const targetExamId = String(selectedProduct.id).trim();
        console.log(`[BuyNowModal] Directly checking purchases for: ${targetExamId} (${selectedProduct.exam_code})`);

        // Check if there's a valid global subscription first
        let hasPremiumAccess = false;

        if (subscriptionInfo && typeof subscriptionInfo === 'object') {
          const now = new Date().getTime();
          const expiryDate = subscriptionInfo.expiresAt ? new Date(subscriptionInfo.expiresAt).getTime() : 0;
          if (expiryDate > now) {
            console.log(`[BuyNowModal] User has active global subscription`);
            hasPremiumAccess = true;
          }
        }

        // If no global subscription, check individual purchases
        if (!hasPremiumAccess) {
          const now = new Date().getTime();

          // Check each purchase
          for (const purchase of purchases) {
            if (!purchase || !purchase.examId) continue;

            const purchaseExamId = String(purchase.examId).trim();
            console.log(`[BuyNowModal] Checking purchase: ${purchaseExamId} against ${targetExamId}`);

            // Check if purchase is active
            if (purchase.status !== 'active') {
              console.log(`[BuyNowModal] Purchase not active: ${purchase.status}`);
              continue;
            }

            // Check if purchase is expired
            if (purchase.expiryDate) {
              try {
                const expiryTime = new Date(purchase.expiryDate).getTime();
                if (expiryTime <= now) {
                  console.log(`[BuyNowModal] Purchase expired: ${purchase.expiryDate}`);
                  continue;
                }
              } catch (error) {
                console.error(`[BuyNowModal] Error parsing expiry date:`, error);
                continue;
              }
            }

            // Check for match (case-insensitive)
            if (purchaseExamId.toLowerCase() === targetExamId.toLowerCase()) {
              console.log(`[BuyNowModal] Found matching active purchase for ${targetExamId}`);
              hasPremiumAccess = true;
              break;
            }
          }
        }

        console.log(`[BuyNowModal] Direct purchase check result: ${hasPremiumAccess}`);

        if (hasPremiumAccess) {
          console.log('[BuyNowModal] User already has premium access (direct check)');
          setProcessingPurchase(false);
          setBuyNowModalVisible(false);

          // Load premium content
          if (typeof loadQnA === 'function' && selectedProduct.exam_code) {
            console.log(`[BuyNowModal] Loading premium content for: ${selectedProduct.exam_code}`);
            //await loadQnA(selectedProduct.exam_code, false, targetExamId);
            await storeSubscriptionStatus(selectedExam.exam_code, true);
          }

          return;
        } else {
          console.log(`[BuyNowModal] User does NOT have premium access to: ${targetExamId} (${selectedProduct.exam_code})`);
        }
      } else {
        console.log(`[BuyNowModal] Cannot check premium access: purchases=${purchases ? purchases.length : 'undefined'}, selectedProduct=${selectedProduct ? 'defined' : 'undefined'}`);
      }

      // Use the optimized BuyNowModal login flow
      /* const result = await handleBuyNowModalLoginFlow({
        targetExam: selectedProduct,
        isSubscriptionActive,
        loadQnA,
        setBuyNowModalVisible,
        navigation,
        setLoading: setProcessingPurchase,
        refreshPurchases
      });

      // If the user already has premium access, we're done
      if (result.hasPremiumAccess) {
        console.log('[BuyNowModal] User already has premium access, flow handled by utility');
        return;
      }

      // If there was an error, log it but continue with purchase flow
      if (!result.success) {
        console.error('[BuyNowModal] Error in login flow:', result.error);
      }

      // Continue with purchase if the user doesn't already have access
      console.log('[BuyNowModal] Continuing with purchase flow - user does not have premium access'); */
    } catch (error) {
      console.error('[BuyNowModal] Error in handleLoginSuccess:', error);
      Alert.alert(
        'Login Error',
        'There was an error processing your login. Please try again.',
        [{ text: 'OK' }]
      );
    } finally {
      setProcessingPurchase(false);
    }
  };

  // Get current subscription plans (only dynamic plans from RevenueCat)
  
  const getSubscriptionPlans = useCallback(() => {
    if (!buyNowModalVisible) return [];
    return getPlansWithCurrentStatus();
  }, [buyNowModalVisible, dynamicPlans, activeSubscription]);

  // Get the selected plan details
  const getSelectedPlanDetails = () => {
    const plans = getSubscriptionPlans();
    return plans.find(p => p.id === selectedPlan) || plans.find(p => p.isPopular);
  };

  // Benefits of upgrading
  const getUpgradeBenefits = () => {
    const plan = getSelectedPlanDetails();
    const aiCredits = plan ? plan.aiCredit : 100;

    // Get total questions from the selected product
    // Handle different ways selectedProduct might be passed
    let totalQuestionsText = 'Unlock all questions (updated regularly)';
    console.log('[getUpgradeBenefits] selectedProduct:',selectedProduct);

    if (selectedProduct) {
      // Case 1: selectedProduct is the full exam object with total_questions
      if (typeof selectedProduct === 'object' && selectedProduct.total_questions) {
        totalQuestionsText = `Unlock all ${selectedProduct.total_questions}+ questions (updated regularly)`;
      }
      // Case 2: selectedProduct has exam_name but no total_questions (from Home.js, Quiz.js)
      else if (typeof selectedProduct === 'object' && selectedProduct.exam_name) {
        // Try to get total questions from the exam's subjects
        if (selectedProduct.subjects && Array.isArray(selectedProduct.subjects)) {
          let totalQuestions = 0;

          // First try to get total_questions from each subject
          selectedProduct.subjects.forEach(subject => {
            if (subject.total_questions) {
              totalQuestions += subject.total_questions;
            } else if (subject.total) {
              // Some subjects might use 'total' instead of 'total_questions'
              totalQuestions += subject.total;
            }
          });

          if (totalQuestions > 0) {
            totalQuestionsText = `Access to ${totalQuestions} questions of this exam`;
          }
        }
      }
      // Case 3: selectedProduct is just the exam_code (from QnA.js)
      else if (typeof selectedProduct === 'string') {
        // We don't have the total questions in this case, so we use the default message
        totalQuestionsText = 'Access to all questions of this exam';
      }
    }

    return [
      totalQuestionsText,
      `Receive ${aiCredits} extra AskAI Credits with every purchase or renewal`,
      'No Ads',
      /*       'Cross-device progress sync' */
    ];
  };

  const handlePurchase = async () => {
    console.log('[debug_purchase_reload] Purchase flow started');
    /* console.log('[debug_purchase_reload] Current plans state:', JSON.stringify(getSubscriptionPlans())); */
    console.log('[debug_purchase_reload] RevenueCat initialized:', revenueCatInitialized);
    console.log('[debug_purchase_reload] Active subscription:', activeSubscription);
    console.log('[debug_purchase_reload] Purchase context available:', purchaseContext ? 'Yes' : 'No');
    console.log('[debug_purchase_reload] Selected product:', selectedProduct);
    console.log('[debug_purchase_reload] Current user:', loginContext?.user?.id || 'anonymous');

    if (!loginContext?.isLoggedIn) {
      console.log('[debug_purchase_reload] User not logged in, showing login modal');
      // Show login modal instead of alert
      setLoginModalVisible(true);
      return;
    }

    if (!selectedPlan) {
      Alert.alert(
        'Select a Plan',
        'Please select a subscription plan to continue.',
        [{ text: 'OK' }]
      );
      return;
    }

    try {
      setProcessingPurchase(true);

      // FIRST: Check if user already has premium access to this exam
      let examId = selectedProduct?.id || selectedProduct?.exam_code || 'default-exam';

      // Ensure examId is always a string
      if (typeof examId === 'object') {
        console.warn('[BuyNowModal] examId is an object, converting to string:', examId);
        examId = examId?.id || examId?.exam_code || String(examId);
      }
      examId = String(examId);

      console.log('[debug_purchase_reload] Checking premium access before purchase');

      // Find the selected plan
      const plans = getSubscriptionPlans();
      const selectedPlanObj = plans.find(p => p.id === selectedPlan);

      if (!selectedPlanObj) {
        throw new Error('Selected plan not found');
      }

      // Check if this is a RevenueCat purchase
      if (selectedPlanObj.revenueCatPackage) {
        console.log('[debug_purchase_reload] Checking RevenueCat initialization status');
        // Skip re-initialization check since we already initialized when modal opened
        if (!revenueCatInitialized) {
          console.log('[debug_purchase_reload] RevenueCat not initialized - should have been initialized when modal opened');
          console.error('[BuyNowModal] RevenueCat not initialized - should have been initialized when modal opened');
          Alert.alert(
            'Purchase Error',
            'Unable to connect to the store. Please try again later.',
            [{ text: 'OK' }]
          );
          setProcessingPurchase(false);
          return;
        }

        /* console.log('[debug_purchase_reload] Processing RevenueCat purchase for:', {
          planName: selectedPlanObj.name,
          productId: selectedPlanObj.productId,
          packageType: selectedPlanObj.revenueCatPackage.packageType,
          offeringId: selectedPlanObj.revenueCatPackage.offeringIdentifier
        }); */

        try {
          /* console.log('[debug_purchase_reload] Calling RevenueCat purchase for package:', selectedPlanObj.revenueCatPackage);
          console.log('[debug_purchase_reload] activeSubscription:', activeSubscription); */
          // Prepare upgrade info if changing from an existing subscription
          /* let upgradeInfo = null;
          if (activeSubscription && activeSubscription.activeSubscriptions.length > 0) {
            upgradeInfo = {
              oldSKU: 'ans_c01',//activeSubscription.productIdentifier,
            };
            console.log('[BuyNowModal] Preparing deferred subscription change from:', activeSubscription.productIdentifier);
          }
          console.log('upgradeInfo:', upgradeInfo) */

          const purchaseResult = await RevenueCatService.makePurchase(
            selectedPlanObj.revenueCatPackage/* ,
            upgradeInfo */
          );
          console.log('[debug_purchase_reload] RevenueCat purchase result:', purchaseResult);

          if (purchaseResult.success) {
            await selectExam(selectedProduct);

            console.log('[debug_purchase_reload] Purchase successful, updating state and context');
            console.log('[debug_purchase_reload] Purchase result details:', {
              transactionId: purchaseResult.transactionIdentifier,
              productId: purchaseResult.productIdentifier,
              purchaseDate: purchaseResult.purchaseDate,
              expirationDate: purchaseResult.expirationDate
            });
            console.log('[BuyNowModal] RevenueCat purchase successful:', purchaseResult);

            // Get the selected plan's AI credits
            const aiCredits = selectedPlanObj.aiCredit || 100;

            // Refresh purchases to get updated data
            /* if (refreshPurchases && typeof refreshPurchases === 'function') {
              console.log('[BuyNowModal] Refreshing purchases after successful purchase');
              await refreshPurchases();
            } */

            /* if (aiCreditContext && typeof aiCreditContext.aiCredits === 'function') {
              console.log(`Adding ${aiCredits} premium AI credits for exam:`, examId);
              await aiCreditContext.addPremiumCredits(examId, aiCredits);
            } */
            console.log(`Adding ${aiCredits} premium AI credits for exam:`, examId);
            await addCredits(aiCredits);

            // Update Store subscription state
            await storeSubscriptionStatus(selectedProduct.exam_code, true)

            // Load premium content with is_free = false
            /* if (typeof loadQnA === 'function' && selectedProduct && selectedProduct.exam_code) {
              console.log('Loading premium content for purchased exam:', selectedProduct.exam_code);
              //await loadQnA(selectedProduct.exam_code, false, examId); // is_free = false for premium content
            } */

            // Update subscription state with full details
            /* const newSubscription = {
              entitlementId: 'pro',
              productIdentifier: selectedPlanObj?.productId || '',
              isActive: true,
              willRenew: true,
              periodType: 'NORMAL',
              expirationDate: purchaseResult?.expirationDate || new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
              originalPurchaseDate: purchaseResult?.purchaseDate || new Date().toISOString(),
              _rawPurchase: purchaseResult || {} // Keep original for debugging
            };

            setActiveSubscription(newSubscription);
            console.groupCollapsed('[debug_subscription] Updated active subscription');
            console.log('New subscription state:', newSubscription);
            console.log('Selected plan:', selectedPlanObj);
            console.log('Purchase result:', {
              productIdentifier: purchaseResult.productIdentifier,
              purchaseDate: purchaseResult.purchaseDate,
              expirationDate: purchaseResult.expirationDate
            }); 
            console.groupEnd();*/

            // Force refresh of plans status
            /* const updatedPlans = getPlansWithCurrentStatus();
            console.groupCollapsed('[debug_subscription] Updated plans status');
            console.log('Plans after update:',
              updatedPlans.map(p => ({
                name: p.name,
                isCurrentPlan: p.isCurrentPlan,
                matchType: p._matchType,
                matchDetails: p._matchDetails
              }))
            );
            console.groupEnd(); */

            // Verify the subscription was properly set
            /* if (!activeSubscription || activeSubscription.productIdentifier !== selectedPlanObj.productId) {
              console.warn('[debug_subscription] Subscription state may not have updated correctly');
              console.log('Current activeSubscription:', activeSubscription);
              console.log('Expected productIdentifier:', selectedPlanObj.productId);
            } */

            // Set purchase as completed
            setPurchaseCompleted(true);

            // Call the completion callback if provided
            /* if (onPurchaseComplete && typeof onPurchaseComplete === 'function') {
              console.log('[BuyNowModal] Calling onPurchaseComplete callback');
              try {
                await onPurchaseComplete({
                  success: true,
                  productId: selectedPlanObj?.productId,
                  examCode: selectedProduct?.exam_code,
                  purchaseResult
                });
              } catch (callbackError) {
                console.error('[BuyNowModal] Error in onPurchaseComplete callback:', callbackError);
              }
            } */

            // Show success alert
            Alert.alert(
              'Purchase Successful',
              `Thank you for your purchase! You now have access to premium content and ${aiCredits} additional AskAI credits.`,
              [{
                text: 'OK',
                onPress: () => {
                  setProcessingPurchase(false);
                  //setPurchaseCompleted(false);
                  setBuyNowModalVisible(false);
                  navigation.reset({
                    index: 0,
                    routes: [{ name: 'MainTabs' }],
                  });
                }
              }]
            );
            return;
          } else if (purchaseResult.cancelled) {
            console.log('[BuyNowModal] RevenueCat purchase was cancelled');
            Alert.alert('Purchase Cancelled', 'The purchase was cancelled.');
            setProcessingPurchase(false);
            return;
          } else {
            throw new Error(purchaseResult.error || 'RevenueCat purchase failed');
          }
        } catch (revenueCatError) {
          console.error('[BuyNowModal] RevenueCat purchase failed:', revenueCatError);

          // Check if this was a user cancellation
          if (revenueCatError.message &&
            (revenueCatError.message.toLowerCase().includes('cancel') ||
              revenueCatError.message.toLowerCase().includes('user cancelled') ||
              revenueCatError.code === 'PURCHASE_CANCELLED' ||
              revenueCatError.code === 'USER_CANCELLED')) {
            console.log('[BuyNowModal] Purchase was cancelled by user');
            Alert.alert('Purchase Cancelled', 'The purchase was cancelled.');
            setProcessingPurchase(false);
            return;
          }

          // For other errors, show error message and don't fall back to test mode
          Alert.alert(
            'Purchase Failed',
            'There was an error processing your purchase. Please try again.',
            [{ text: 'OK' }]
          );
          setProcessingPurchase(false);
          return;
        }
      }

      // If we reach here, no RevenueCat package was available or RevenueCat is not initialized
      Alert.alert(
        'Purchase Unavailable',
        'In-app purchases are not available at the moment. Please try again later.',
        [{ text: 'OK' }]
      );
      setProcessingPurchase(false);
    } catch (error) {
      console.error('Error:', error);
      console.log('Error stack:', error.stack);
      console.log('Selected plan:', selectedPlan);
      console.log('Selected product:', selectedProduct);
      console.log('Purchase context state:', {
        purchases: purchases?.length || 0,
        subscriptionInfo,
        /* isSubscriptionActive: typeof isSubscriptionActive === 'function' ?
          isSubscriptionActive(selectedProduct?.id || selectedProduct?.exam_code) : 'N/A' */
      });

      // Provide more detailed error message
      let errorMessage = 'There was an error processing your purchase. Please try again.';
      if (error.message) {
        errorMessage = error.message;
      }

      // Reset purchase completed state
      setPurchaseCompleted(false);

      // Attempt recovery by refreshing purchases
      /* try {
        console.log('[debug_purchase_error] Attempting recovery by refreshing purchases');
        await refreshPurchases();
        console.log('[debug_purchase_error] Purchases refreshed successfully');
      } catch (refreshError) {
        console.error('[debug_purchase_error] Failed to refresh purchases:', refreshError);
      } */

      // Show error alert with recovery options
      Alert.alert(
        'Purchase Failed',
        errorMessage,
        [{
          text: 'Try Again',
          onPress: () => {
            setProcessingPurchase(false);
            handlePurchase(); // Retry the purchase
          }
        }, {
          text: 'Cancel',
          onPress: () => {
            setProcessingPurchase(false);
          },
          style: 'cancel'
        }]
      );
    }
  };

  // Render subscription plan card
  const renderPlanCard = (plan) => (
    <Pressable
      key={plan.id}
      style={[
        styles.planCard,
        selectedPlan === plan.id && styles.selectedPlanCard,
        {
          borderColor: selectedPlan === plan.id ? colors.primary : 'rgba(0,0,0,0.08)',
          backgroundColor: colors.surface,
          opacity: processingPurchase ? 0.5 : 1
        }
      ]}
      onPress={() => !processingPurchase && setSelectedPlan(plan.id)}
      disabled={processingPurchase}
    >
      <RadioButton
        value={plan.id}
        status={selectedPlan === plan.id ? 'checked' : 'unchecked'}
        color={colors.primary}
      />
      <View style={styles.planContent}>
        <View style={styles.planHeader}>
          <Text style={[
            styles.planName,
            {
              color: selectedPlan === plan.id ? colors.primary : colors.onSurface
            }
          ]} maxFontSizeMultiplier={1.2} >
            {plan.name}
          </Text>
          {plan?.isCurrentPlan && (
            <Surface style={[styles.currentPlanBadge, { backgroundColor: '#4CAF50' }]}>
              <Text style={[styles.currentPlanText, { color: '#FFFFFF' }]} maxFontSizeMultiplier={1.5}  >Current Plan</Text>
            </Surface>
          )}
          {plan?.isPopular && !plan?.isCurrentPlan && (
            <Surface style={[styles.popularBadge, { backgroundColor: colors.primary }]}>
              <Text style={[styles.popularText, { color: '#FFFFFF' }]} maxFontSizeMultiplier={1.5}  >Popular</Text>
            </Surface>
          )}
        </View>

        <View style={styles.priceContainer}>
          <View style={styles.priceWrapper}>
            <Text style={[styles.discountPrice, { color: colors.onSurface }]}>
              {plan.discountPrice}
            </Text>
          </View>
        </View>
      </View>
    </Pressable>
  );

  return (
    <Portal>
      {/* Login Modal */}
      <LoginModal
        visible={loginModalVisible}
        onDismiss={() => setLoginModalVisible(false)}
        onLoginSuccess={handleLoginSuccess}
        source="buyNow"
        navigation={navigation}
        targetExam={selectedProduct}
        setBuyNowModalVisible={setBuyNowModalVisible}
      />

      {/* Main Purchase Modal */}
      <Modal
        visible={buyNowModalVisible}
        onDismiss={() => {
          // Only allow dismissal if not processing a purchase
          if (!processingPurchase) {
            setBuyNowModalVisible(false);
          }
        }}
        dismissable={!processingPurchase} // Prevent dismissal during purchase processing
        contentContainerStyle={[
          styles.modalContent,
          { backgroundColor: colors.background }
        ]}
      >
        {/* Loading overlay during purchase processing */}
        {processingPurchase && (
          <View style={styles.loadingOverlay} pointerEvents="auto">
            <View style={styles.loadingContent}>
              <ActivityIndicator 
                size="large" 
                color="#FFFFFF"
                style={styles.loadingIndicator}
             />
              <Text style={styles.loadingText}>
                Processing your purchase...
              </Text>
            </View>
          </View>
        )}
        <View style={[styles.container, { backgroundColor: colors.background }]}>
          <IconButton
            icon="close"
            size={24}
            onPress={() => {
              // Only allow closing if not processing a purchase
              if (!processingPurchase) {
                setBuyNowModalVisible(false);
              }
            }}
            style={[
              styles.closeButton,
              // Reduce opacity when disabled
              processingPurchase && { opacity: 0.5 }
            ]}
            disabled={processingPurchase}
            color={colors.onSurface}
          />

          <ScrollView
            showsVerticalScrollIndicator={true}
            contentContainerStyle={styles.scrollViewContent}
            scrollEnabled={!processingPurchase}
          >

            <Text style={[styles.modalTitle, { color: colors.onSurface }]} allowFontScaling={false} >
              {selectedProduct?.exam_name
                ? `Premium Access for \n${selectedProduct.exam_name}`
                : 'Premium Subscription'}
            </Text>

            {/* Development-only indicators */}
            {__DEV__ && (
              <>
                {/* Test mode indicator */}
                <View style={[styles.testModeContainer, { backgroundColor: colors.errorContainer }]}>
                  <Text style={[styles.testModeText, { color: colors.error }]}>
                    TEST MODE - No actual payment will be processed
                  </Text>
                </View>

                {/* RevenueCat status indicator */}
                <View style={[styles.statusContainer, {
                  backgroundColor: revenueCatInitialized ? colors.primaryContainer : colors.surfaceVariant
                }]}>
                  <Text style={[styles.statusText, {
                    color: revenueCatInitialized ? colors.primary : colors.onSurfaceVariant
                  }]}>
                    {revenueCatInitialized
                      ? (() => {
                        const examCode = selectedProduct?.exam_code || (typeof selectedProduct === 'string' ? selectedProduct : null);
                        const availablePlans = getSubscriptionPlans();
                        const examSpecificPlans = availablePlans.filter(p => p.examCode);

                        if (examSpecificPlans.length > 0) {
                          return `✅ ${examSpecificPlans.length} plans for ${examCode?.toUpperCase()}`;
                        } else if (availablePlans.length === 0 && examCode) {
                          return `⚠️ No plans available for ${examCode.toUpperCase()}`;
                        } else {
                          return `✅ RevenueCat Active (${dynamicPlans.length} plans loaded)`;
                        }
                      })()
                      : revenueCatError
                        ? `⚠️ Service temporarily unavailable`
                        : `⏳ Loading subscription options...`
                    }
                  </Text>
                </View>
              </>
            )}

            {loginContext.isLoading ? (
              <View style={styles.loadingContainer}>
                <ActivityIndicator size="large" color={colors.primary} />
                <Text style={{ color: colors.onSurface, marginTop: 16 }}>
                  {loginContext.isInitialized ? 'Processing login...' : 'Initializing...'}
                </Text>
              </View>
            ) : revenueCatLoading ? (
              <View style={styles.loadingContainer}>
                <ActivityIndicator size="large" color={colors.primary} />
                <Text style={{ color: colors.onSurface, marginTop: 16 }}>
                  Loading subscription options...
                </Text>
                {revenueCatError && (
                  <Pressable onPress={() => initializeRevenueCat()}>
                    <Text style={{ color: colors.error, marginTop: 8, textAlign: 'center' }}>
                      Failed to load - Tap to retry
                    </Text>
                  </Pressable>
                )}
              </View>
            ) : processingPurchase ? (
              <View style={styles.loadingContainer}>
                <ActivityIndicator size="large" color={colors.primary} />
                <Text style={{ color: colors.onSurface, marginTop: 16 }}>
                  {purchaseCompleted
                    ? "Success! Setting up your premium access..."
                    : "Processing your subscription..."}
                </Text>
              </View>
            ) : (
              <>
                {/* Benefits section */}
                <View style={[styles.benefitsContainer, { borderColor: selectedPlan ? colors.primary : 'rgba(0,0,0,0.08)', backgroundColor: colors.surface }]}>
                  <Text style={[styles.benefitsTitle, { color: colors.primary }]} maxFontSizeMultiplier={1.5}  >
                    What's Included:
                  </Text>
                  {getUpgradeBenefits().map((benefit, index) => (
                    <View key={index} style={styles.benefitRow}>
                      <Text style={[styles.benefitBullet, { color: colors.primary }]} maxFontSizeMultiplier={1.5} >•</Text>
                      <Text style={[styles.benefitText, { color: colors.onSurface }]} maxFontSizeMultiplier={1.5} >
                        {benefit}
                      </Text>
                    </View>
                  ))}
                </View>

                {getSubscriptionPlans().length > 0 ? (
                  <View style={styles.plansContainer}>
                    {getSubscriptionPlans().map(renderPlanCard)}
                  </View>
                ) : (
                  <View style={[styles.noPlansContainer, { backgroundColor: colors.surfaceVariant }]}>
                    <Text style={[styles.noPlansTitle, { color: colors.onSurfaceVariant }]}>
                      Coming Soon
                    </Text>
                    <Text style={[styles.noPlansText, { color: colors.onSurfaceVariant }]}>
                      {selectedProduct?.exam_code
                        ? `Premium plans for ${selectedProduct.exam_code.toUpperCase()} are not yet available.`
                        : 'Premium plans for this exam are not yet available.'
                      }
                    </Text>
                    <Text style={[styles.noPlansSubtext, { color: colors.onSurfaceVariant }]}>
                      We're working on adding more exam options. Check back soon!
                    </Text>
                  </View>
                )}
                {getSubscriptionPlans().length > 0 ? (
                  <View style={styles.buttonsContainer}>
                    {(() => {
                      const plans = getSubscriptionPlans();
                      const selectedPlanObj = plans.find(p => p.id === selectedPlan);
                      const currentPlan = plans.find(p => p.isCurrentPlan);
                      const hasCurrentPlan = selectedPlanObj?.isCurrentPlan;
                      const examId = selectedProduct?.id || selectedProduct?.exam_code;

                      // Compare prices to determine if this is an upgrade or downgrade
                      let isUpgrade = false;
                      if (currentPlan && selectedPlanObj) {
                        console.log('[debug_subscription] Comparing plans for upgrade/downgrade:', {
                          currentPlan: currentPlan.name,
                          currentPrice: currentPlan.price,
                          selectedPlan: selectedPlanObj.name,
                          selectedPrice: selectedPlanObj.price
                        });

                        try {
                          const currentPrice = parseFloat(currentPlan.price.replace(/[^0-9.]/g, '')) || 0;
                          const selectedPrice = parseFloat(selectedPlanObj.price.replace(/[^0-9.]/g, '')) || 0;
                          isUpgrade = selectedPrice > currentPrice;

                          console.log('[debug_subscription] Price comparison result:', {
                            currentPrice,
                            selectedPrice,
                            isUpgrade
                          });
                        } catch (error) {
                          console.error('[debug_subscription] Error comparing prices:', error);
                        }
                      }

                      return (
                        <>
                          {currentPlan && selectedPlanObj && !hasCurrentPlan && (
                            <View style={styles.upgradeNoteContainer}>
                              <Text style={[styles.upgradeText, { color: colors.onSurface }]}>
                                Plan changes take effect at next renewal
                              </Text>
                            </View>
                          )}

                          <FixedFontButton
                            mode="contained"
                            onPress={/* hasCurrentPlan || hasPremiumAccess ? null :  */ handlePurchase}
                            style={[
                              styles.purchaseButton,
                              {
                                backgroundColor: /* hasCurrentPlan || hasPremiumAccess ? '#4CAF50' : */ colors.primary
                              }
                            ]}
                            labelStyle={{ color: '#FFFFFF', fontWeight: '600' }}
                            loading={processingPurchase}
                            disabled={processingPurchase || !selectedPlan || getSubscriptionPlans().length === 0 /*||  hasCurrentPlan || hasPremiumAccess */}
                          >
                            {/* hasCurrentPlan || hasPremiumAccess ? 'Current Plan' :
                              currentPlan ?
                                'Change Plan' : */
                                'Start Premium Access'}
                          </FixedFontButton>
                        </>
                      );
                    })()}

                    {/* Terms text moved under the button */}
                    <View style={styles.termsContainer}>
                      <Text style={[styles.termsText, { color: colors.onSurfaceVariant }]}>
                        By continuing, you agree to our Terms of Service and Privacy Policy. Subscriptions auto-renew unless cancelled 24 hours before renewal.
                      </Text>
                    </View>
                  </View>
                ) : (
                  null
                )}
              </>
            )}

          </ScrollView>
        </View>
      </Modal>
    </Portal>
  );
};

export default BuyNowModal;

const styles = StyleSheet.create({
  loadingOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0,0,0,0.8)',
    zIndex: 99999,
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 1000, // For Android
  },
  loadingContent: {
    backgroundColor: 'rgba(0,0,0,0.8)',
    padding: 20,
    borderRadius: 10,
    alignItems: 'center',
  },
  loadingText: {
    color: '#FFFFFF',
    marginTop: 16,
    fontSize: 16,
    fontWeight: '500',
    textAlign: 'center',
  },
  loadingIndicator: {
    marginBottom: 16,
  },
  modalContent: {
    padding: 0,
    margin: 0,
    borderRadius: 0,
    height: '100%',
    width: '100%',
  },
  scrollViewContent: {
    paddingBottom: 30, // Add extra padding at the bottom for better scrolling
  },
  container: {
    flex: 1,
    paddingHorizontal: 16,
    paddingTop: 30
  },
  closeButton: {
    position: 'absolute',
    right: 5,
    top: 5,
    zIndex: 1,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginTop: 16,
    marginBottom: 8,
    textAlign: 'center',
  },
  modalSubtitle: {
    fontSize: 16,
    marginBottom: 16,
    textAlign: 'center',
    lineHeight: 22,
  },
  testModeContainer: {
    padding: 8,
    marginHorizontal: 16,
    marginBottom: 16,
    borderRadius: 8,
    alignItems: 'center',
  },
  testModeText: {
    fontWeight: 'bold',
    fontSize: 14,
  },
  statusContainer: {
    padding: 8,
    marginHorizontal: 16,
    marginBottom: 16,
    borderRadius: 8,
    alignItems: 'center',
  },
  statusText: {
    fontWeight: '500',
    fontSize: 12,
  },
  subtitle: {
    fontSize: 16,
    marginBottom: 16,
    textAlign: 'center',
    fontWeight: '500',
  },
  benefitsContainer: {
    marginBottom: 16,
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
  },
  benefitsTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  benefitRow: {
    flexDirection: 'row',
    marginBottom: 6,
    alignItems: 'flex-start',
  },
  benefitBullet: {
    fontSize: 16,
    marginRight: 8,
    lineHeight: 20,
  },
  benefitText: {
    fontSize: 14,
    flex: 1,
    lineHeight: 20,
  },
  planCard: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    padding: 16,
    marginBottom: 12,
    borderRadius: 12,
    borderWidth: 1,
    elevation: 1,
  },
  selectedPlanCard: {
    borderWidth: 2,
  },
  planContent: {
    flex: 1,
    marginLeft: 12,
  },
  planHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 4,
  },
  planName: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  popularBadge: {
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 4,
  },
  popularText: {
    fontSize: 12,
    fontWeight: 'bold',
  },
  currentPlanBadge: {
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 4,
  },
  currentPlanText: {
    fontSize: 12,
    fontWeight: 'bold',
  },
  priceContainer: {
    flexDirection: 'row',
    alignItems: 'baseline',
    marginBottom: 8,
  },
  priceWrapper: {
    flexDirection: 'row',
    alignItems: 'baseline',
    marginRight: 4,
  },
  discountPrice: {
    fontSize: 20,
    fontWeight: 'bold',
    marginRight: 6,
  },
  originalPrice: {
    fontSize: 16,
    marginRight: 4,
  },
  period: {
    fontSize: 14,
  },
  description: {
    fontSize: 14,
    lineHeight: 20,
  },
  termsContainer: {
    marginTop: 12,
    marginBottom: 8,
  },
  termsText: {
    fontSize: 11,
    textAlign: 'center',
    lineHeight: 16,
  },
  buttonsContainer: {
    marginTop: 8,
  },
  purchaseButton: {
    borderRadius: 8,
    paddingVertical: 12,
    marginBottom: 0,
  },
  testSubscriptionButton: {
    borderRadius: 8,
    paddingVertical: 8,
    marginBottom: 8,
  },
  restoreButton: {
    borderRadius: 8,
  },
  loadingContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 24,
    marginTop: 40,
  },
  loginRequiredContainer: {
    alignItems: 'center',
    padding: 24,
  },
  loginRequiredText: {
    textAlign: 'center',
    marginBottom: 16,
    fontSize: 16,
  },
  loginButton: {
    borderRadius: 8,
    paddingVertical: 8,
    width: '100%',
  },
  currencyCode: {
    fontSize: 12,
    marginLeft: 4,
    fontWeight: '500',
  },
  examCode: {
    fontSize: 12,
    marginTop: 4,
    fontWeight: '500',
  },
  noPlansContainer: {
    padding: 24,
    marginHorizontal: 16,
    marginVertical: 16,
    borderRadius: 12,
    alignItems: 'center',
  },
  noPlansTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 8,
    textAlign: 'center',
  },
  noPlansText: {
    fontSize: 14,
    textAlign: 'center',
    marginBottom: 8,
    lineHeight: 20,
  },
  noPlansSubtext: {
    fontSize: 12,
    textAlign: 'center',
    fontStyle: 'italic',
  },
  upgradeNoteContainer: {
    marginBottom: 12,
    paddingHorizontal: 8,
    alignItems: 'center',
  },
  upgradeText: {
    fontSize: 14,
    textAlign: 'center',
    fontStyle: 'italic',
    lineHeight: 20,
  },
});