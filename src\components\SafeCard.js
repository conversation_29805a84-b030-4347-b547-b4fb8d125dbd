import React, { useState, useEffect, useRef } from 'react';
import { View, StyleSheet } from 'react-native';
import { Card, useTheme } from 'react-native-paper';

/**
 * A wrapper around react-native-paper's Card component that prevents animation
 * conflicts during theme switching.
 *
 * This component renders a regular Card when it's safe to do so, and falls back
 * to a View with similar styling during theme transitions.
 */
const SafeCard = ({ children, style, mode = 'elevated', ...props }) => {
  const { colors } = useTheme();
  const [isThemeChanging, setIsThemeChanging] = useState(false);
  const timeoutRef = useRef(null);

  // Listen for theme changes in the parent component
  useEffect(() => {
    // When the component mounts or the theme changes, temporarily use the safe version
    setIsThemeChanging(true);

    // Clear any existing timeout
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    // After a short delay, switch back to the regular Card
    timeoutRef.current = setTimeout(() => {
      setIsThemeChanging(false);
    }, 300); // Wait for theme transition to complete

    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, [colors]);

  // If theme is changing, use a simple View with similar styling
  if (isThemeChanging) {
    const viewStyle = [
      styles.safeCard,
      {
        backgroundColor: colors.surface,
        borderColor: mode === 'outlined' ? colors.outline : 'transparent',
      },
      style
    ];

    return (
      <View style={viewStyle} {...props}>
        {children}
      </View>
    );
  }

  // Otherwise, use the regular Card component
  return (
    <Card style={style} mode={mode} {...props}>
      {children}
    </Card>
  );
};

const styles = StyleSheet.create({
  safeCard: {
    borderWidth: 1,
    borderRadius: 12,
    overflow: 'hidden',
  },
  safeCardContent: {
    padding: 16,
  }
});

/**
 * A wrapper around react-native-paper's Card.Content component that prevents animation
 * conflicts during theme switching.
 */
const SafeCardContent = ({ children, style, ...props }) => {
  const { colors } = useTheme();
  const [isThemeChanging, setIsThemeChanging] = useState(false);
  const timeoutRef = useRef(null);

  // Listen for theme changes in the parent component
  useEffect(() => {
    // When the component mounts or the theme changes, temporarily use the safe version
    setIsThemeChanging(true);

    // Clear any existing timeout
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    // After a short delay, switch back to the regular Card.Content
    timeoutRef.current = setTimeout(() => {
      setIsThemeChanging(false);
    }, 300); // Wait for theme transition to complete

    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, [colors]);

  // If theme is changing, use a simple View with similar styling
  if (isThemeChanging) {
    return (
      <View style={[styles.safeCardContent, style]} {...props}>
        {children}
      </View>
    );
  }

  // Otherwise, use the regular Card.Content component
  return (
    <Card.Content style={style} {...props}>
      {children}
    </Card.Content>
  );
};

// Add the Content component to SafeCard
SafeCard.Content = SafeCardContent;

export default SafeCard;
