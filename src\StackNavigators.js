import React from 'react';
import {createStackNavigator} from '@react-navigation/stack';
import QnA from './QnA';
import QnAList from './QnAList';
import Quiz from './Quiz';
import QuizMode from './QuizMode';
import QuizResult from './QuizResult';
import Setting from './Setting';
import InActivePlan from './InActivePlan';
import QuizSetting from './QuizSetting';
import QnAFilter from './QnAFilter';
import QnaDetail from './QnADetail';
import ChatScreen from './AIChat';
import AdmobTestScreen from './components/AdmobTestScreen';
import CopilotTestScreen from './components/CopilotTestScreen';

const QuestionStack = createStackNavigator();
export const QuestionStackNavigator = () => (
  <QuestionStack.Navigator screenOptions={{headerShown: false}}>
    <QuestionStack.Screen name="QnA" component={QnA} />
    <QuestionStack.Screen name="QnAList" component={QnAList} />
    <QuestionStack.Screen name="QnAFilter" component={QnAFilter} />
    <QuestionStack.Screen name="QnADetail" component={QnaDetail} />
    <QuestionStack.Screen name="ChatScreen" component={ChatScreen} />
  </QuestionStack.Navigator>
);

const QuizStack = createStackNavigator();
export const QuizStackNavigator = () => (
  <QuizStack.Navigator screenOptions={{headerShown: false}}>
    <QuizStack.Screen name="QuizScreen" component={Quiz} />
    <QuizStack.Screen name="QuizSetting" component={QuizSetting} />
    <QuizStack.Screen name="QuizMode" component={QuizMode} />
    <QuizStack.Screen name="QuizResult" component={QuizResult} />
  </QuizStack.Navigator>
);

const SettingStack = createStackNavigator();

export const SettingStackNavigator = () => (
  <SettingStack.Navigator screenOptions={{headerShown: false}}>
    <SettingStack.Screen name="Settings" component={Setting} />
    <SettingStack.Screen name="InActivePlan" component={InActivePlan} />
    <SettingStack.Screen name="AdmobTestScreen" component={AdmobTestScreen} />
    <SettingStack.Screen name="CopilotTestScreen" component={CopilotTestScreen} />
  </SettingStack.Navigator>
);