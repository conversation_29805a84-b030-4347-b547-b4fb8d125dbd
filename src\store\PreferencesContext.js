// contexts/PreferencesContext.js
import React, { createContext, useState, useMemo, useEffect, useContext } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {
    DarkTheme as NavigationDarkTheme,
    DefaultTheme as NavigationDefaultTheme,
} from '@react-navigation/native';
import {
    PaperProvider,
    MD3DarkTheme,
    MD3LightTheme,
    configureFonts,
    MD2DarkTheme,
    MD2LightTheme,
} from 'react-native-paper';
import { darkTheme, lightTheme } from '../config/constants/theme';

export const PreferencesContext = createContext();

export const PreferencesProvider = ({ children }) => {
    // Theme state management
    const [isDarkMode, setIsDarkMode] = useState(false);
    const [themeVersion, setThemeVersion] = useState(3);
    const [customFontLoaded, setCustomFont] = useState(false);
    const [rippleEffectEnabled, setRippleEffectEnabled] = useState(true);
    const [shouldShuffleChoices, setShouldShuffleChoices] = useState(true);

    // Theme configuration
    const { LightTheme, DarkTheme } = {
        reactNavigationLight: NavigationDefaultTheme,
        reactNavigationDark: NavigationDarkTheme,
    };

    // Base theme selection
    const PaperLightTheme = useMemo(
        () => (themeVersion === 2 ? MD2LightTheme : MD3LightTheme),
        [themeVersion]
    );
    const PaperDarkTheme = useMemo(
        () => (themeVersion === 2 ? MD2DarkTheme : MD3DarkTheme),
        [themeVersion]
    );

    // Combined themes
    const CombinedDefaultTheme = useMemo(
        () => ({
            ...PaperLightTheme,
            ...LightTheme,
            ...lightTheme,
        }),
        [PaperLightTheme, LightTheme]
    );

    const CombinedDarkTheme = useMemo(
        () => ({
            ...PaperDarkTheme,
            ...DarkTheme,
            ...darkTheme,
        }),
        [PaperDarkTheme, DarkTheme]
    );

    // Current theme selection
    const combinedTheme = useMemo(
        () => (isDarkMode ? CombinedDarkTheme : CombinedDefaultTheme),
        [isDarkMode, CombinedDarkTheme, CombinedDefaultTheme]
    );

    // Font configuration
    const configuredFontTheme = useMemo(
        () => ({
            ...combinedTheme,
            fonts: configureFonts({ config: { fontFamily: 'System' } }),
        }),
        [combinedTheme]
    );

    // Final theme
    const theme = useMemo(
        () => (customFontLoaded ? configuredFontTheme : combinedTheme),
        [customFontLoaded, configuredFontTheme, combinedTheme]
    );

    // Theme toggle handlers with animation safety
    const toggleTheme = () => {
        // Use requestAnimationFrame to ensure we're not in the middle of an animation cycle
        requestAnimationFrame(() => {
            setIsDarkMode(prev => {
                const newValue = !prev;
                // Store the preference asynchronously
                AsyncStorage.setItem('darkMode', newValue.toString())
                    .catch(err => console.error('Failed to save theme preference:', err));
                return newValue;
            });
        });
    };

    // Add this useEffect to load persisted preferences
    useEffect(() => {
        const loadPreferences = async () => {
            try {
                const storedDarkMode = await AsyncStorage.getItem('darkMode');
                if (storedDarkMode !== null) {
                    setIsDarkMode(storedDarkMode === 'true');
                }
                const storedShuffle = await AsyncStorage.getItem('shouldShuffleChoices');
                if (storedShuffle !== null) {
                    setShouldShuffleChoices(storedShuffle === 'true');
                }
            } catch (error) {
                console.error('Error loading preferences:', error);
            }
        };
        loadPreferences();
    }, []);

    // Add this toggle handler with animation safety
    const toggleShuffleChoices = () => {
        // Use requestAnimationFrame to ensure we're not in the middle of an animation cycle
        requestAnimationFrame(() => {
            setShouldShuffleChoices(prev => {
                const newValue = !prev;
                // Store the preference asynchronously
                AsyncStorage.setItem('shouldShuffleChoices', newValue.toString())
                    .catch(err => console.error('Failed to save shuffle preference:', err));
                return newValue;
            });
        });
    };


    // Memoized preferences value
    const preferences = useMemo(
        () => ({
            isDarkMode,
            themeVersion,
            customFontLoaded,
            rippleEffectEnabled,
            shouldShuffleChoices,
            theme,
            toggleTheme,
            toggleShuffleChoices,
        }),
        [isDarkMode, themeVersion, customFontLoaded, rippleEffectEnabled, shouldShuffleChoices, theme]
    );

    return (
        <PreferencesContext.Provider value={preferences}>
            {children}
        </PreferencesContext.Provider>
    );
};

export const usePreferencesContext = () => useContext(PreferencesContext);