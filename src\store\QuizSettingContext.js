import React, { createContext, useState, useContext } from 'react';

const QuizSettingContext = createContext();

export const QuizSettingProvider = ({ children }) => {
  const [appliedFilters, setAppliedFilters] = useState({
    subjects: [],
    dateRange: {
      startDate: null,
      endDate: null
    },
    timeLimit: 30,
    onlyIncorrect: false,
    showAnswers: false
  });

  return (
    <QuizSettingContext.Provider value={{ appliedFilters, setAppliedFilters }}>
      {children}
    </QuizSettingContext.Provider>
  );
};

export const useQuizSetting = () => {
  const context = useContext(QuizSettingContext);
  if (!context) {
    throw new Error('useQuizSetting must be used within a QuizSettingProvider');
  }
  return context;
};