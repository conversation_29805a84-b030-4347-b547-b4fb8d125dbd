import { shuffleArray } from './shuffleArray';

export const shuffleAndReorderChoices = (originalChoices, originalAnswer) => {
  const originalValues = originalChoices.map(c => Object.values(c)[0]);
  const shuffledValues = shuffleArray(originalValues);

  const n = originalChoices.length;
  const newKeys = Array.from({ length: n }, (_, i) => String.fromCharCode(65 + i));

  const shuffledChoices = newKeys.map((key, index) => ({
    [key]: shuffledValues[index]
  }));

  const originalToNewKeyMap = {};
  const newKeyToOriginalKeyMap = {};

  originalChoices.forEach((choice, originalIndex) => {
    const originalKey = Object.keys(choice)[0];
    const originalValue = originalValues[originalIndex];
    const newIndex = shuffledValues.indexOf(originalValue);
    const newKey = newKeys[newIndex];
    originalToNewKeyMap[originalKey] = newKey;
    newKeyToOriginalKeyMap[newKey] = originalKey;
  });

  const mappedAnswer = originalAnswer.map(k => originalToNewKeyMap[k]);

  return {
    shuffledChoices,
    mappedAnswer,
    newKeyToOriginalKeyMap,
    originalToNewKeyMap
  };
};