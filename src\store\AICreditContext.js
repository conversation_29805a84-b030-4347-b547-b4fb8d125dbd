import React, { createContext, useContext, useState, useEffect } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { usePurchase } from './PurchaseContext';
import { useLogin } from './LoginContext';
import RealTimeSyncService from '../services/RealTimeSyncService';

// Create the context
const AICreditContext = createContext();

// Default credit amounts
const DEFAULT_FREE_CREDITS = 10;
const DEFAULT_PREMIUM_CREDITS = 200;

/**
 * Provider component for AI credit state
 */
export const AICreditProvider = ({ children }) => {
  const [credits, setCredits] = useState(0);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  // Load credits on mount
  useEffect(() => {
    const initialize = async () => {
      try {
        await loadCredits();
      } catch (error) {
        console.error('Error initializing AICreditContext:', error);
        // Don't show error to user, just set default credits
        setCredits(DEFAULT_FREE_CREDITS);
      }
    };

    initialize();
  }, []);

  /**
   * Load credits for a specific user
   * @param {string} uid - User ID
   */
  const loadCredits = async () => {
    try {
      setLoading(true);
      setError(null);
      
      // Load credits from AsyncStorage using unified key
      const creditsJson = await AsyncStorage.getItem('ai_credits');

      if (creditsJson) {
        const parsedCredits = JSON.parse(creditsJson);
        // Ensure credits is a valid number
        const validCredits = Number.isFinite(parsedCredits.credits) 
          ? parsedCredits.credits 
          : DEFAULT_FREE_CREDITS;
        setCredits(validCredits);
      } else {
        // If no credits found, set default credits
        const defaultCredits = DEFAULT_FREE_CREDITS;
        setCredits(defaultCredits);
        await AsyncStorage.setItem('ai_credits', JSON.stringify({ 
          credits: defaultCredits,
          lastUpdated: new Date().toISOString()
        }));
      }
    } catch (error) {
      console.error('Error loading credits:', error);
      setError('Failed to load credits');
      setCredits(DEFAULT_FREE_CREDITS);
    } finally {
      setLoading(false);
    }
  };

  /**
   * Save credits to AsyncStorage
   * @param {number} creditsToSave - Credits to save
   * @param {string} uid - User ID
   */
  const saveCredits = async (creditsToSave) => {
    try {
      await AsyncStorage.setItem('ai_credits', JSON.stringify({ credits: creditsToSave }));
    } catch (error) {
      console.error('Error saving credits:', error);
      setError('Failed to save credits');
    }
  };

  /**
   * Add credits to user's account
   * @param {number} amount - Amount of credits to add
   * @returns {number} - New credit balance
   */
  const addCredits = async (amount) => {
    try {
      setLoading(true);
      setError(null);

      // Validate input amount
      const validAmount = Number.isFinite(amount) ? Math.max(0, amount) : 0;
      const currentCredits = Number.isFinite(credits) ? credits : DEFAULT_FREE_CREDITS;
      
      const newCredits = currentCredits + validAmount;
      setCredits(newCredits);

      // Always save to AsyncStorage
      await saveCredits(newCredits);

      // Sync AI credit balance to MongoDB in real-time (only for logged in users)
      /*  try {
          console.log('[AICreditContext] Syncing AI credit balance to MongoDB...');
          const transaction = {
            type: 'purchase',
            amount: amount,
            creditAmount: amount,
            transactionId: `ai_credit_add_${Date.now()}`,
            purchaseDate: new Date().toISOString(),
            uniqueId: `ai_credit_${userId}_${Date.now()}`
          };
  
          const mongoResponse = await RealTimeSyncService.syncAICreditBalanceToMongoDB(userId, newCredits, transaction);
          console.log('[AICreditContext] AI credit balance synced to MongoDB successfully:', mongoResponse);
        } catch (syncError) {
          console.error('[AICreditContext] Failed to sync AI credit balance to MongoDB:', syncError);
          // Continue with local storage even if MongoDB sync fails
        } */

      return newCredits;
    } catch (error) {
      console.error('Error adding credits:', error);
      setError(error.message || 'Failed to add credits');
      // Don't throw error, just return current credits
      return credits;
    } finally {
      setLoading(false);
    }
  };

  /**
   * Use credits (deduct from balance)
   * @param {number} amount - Amount of credits to use
   * @returns {boolean} - Whether the operation was successful
   */
  const useCredits = async (amount = 1) => {
    try {
      // Validate input amount
      const validAmount = Math.max(0, Number(amount) || 1);
      const currentCredits = Number.isFinite(credits) ? credits : DEFAULT_FREE_CREDITS;
      
      // Check if user has enough credits
      if (currentCredits < validAmount) {
        console.warn('Cannot use credits: Not enough credits');
        return false;
      }

      setLoading(true);
      setError(null);

      const newCredits = Math.max(0, currentCredits - validAmount);
      setCredits(newCredits);
      await saveCredits(newCredits);

      return true;
    } catch (error) {
      console.error('Error using credits:', error);
      setError('Failed to use credits');
      return false;
    } finally {
      setLoading(false);
    }
  };

  /**
   * Add premium credits when user purchases premium access
   * @param {string} examId - Exam ID that was purchased
   * @param {number} creditAmount - Amount of credits to add (defaults to DEFAULT_PREMIUM_CREDITS)
   */
  const addPremiumCredits = async (examId, creditAmount = DEFAULT_PREMIUM_CREDITS) => {
    try {
      setLoading(true);
      setError(null);

      // Add the specified amount of premium credits
      const newCredits = credits + creditAmount;
      setCredits(newCredits);
      await saveCredits(newCredits);

      // Sync premium AI credit purchase to MongoDB in real-time using RealTimeSyncService
      try {
        console.log('[AICreditContext] Syncing premium AI credit purchase to MongoDB via RealTimeSyncService...');
        const creditData = {
          amount: creditAmount,
          creditAmount: creditAmount,
          examId: examId,
          transactionId: `premium_credits_${examId}_${Date.now()}`,
          purchaseDate: new Date().toISOString(),
          paymentMethod: 'premium_purchase',
          price: 'Included with premium'
        };

        const mongoResponse = await RealTimeSyncService.syncAICreditPurchaseToMongoDB(creditData);
        console.log('[AICreditContext] Premium AI credit purchase synced to MongoDB successfully:', mongoResponse);
      } catch (syncError) {
        console.error('[AICreditContext] Failed to sync premium AI credit purchase to MongoDB:', syncError);
        // Continue with local storage even if MongoDB sync fails
      }

      console.log(`Added ${creditAmount} premium credits for exam ${examId}. New total: ${newCredits}`);
      return newCredits;
    } catch (error) {
      console.error('Error adding premium credits:', error);
      setError(error.message || 'Failed to add premium credits');
      return credits; // Return current credits on error
    } finally {
      setLoading(false);
    }
  };

  /**
   * Clear credits when user logs out
   */
  const clearCredits = async () => {
    try {
      const emptyCreditData = {
        credits: 0,
        totalCredits: 0,
        usedCredits: 0,
        transactions: [],
        lastUpdated: new Date().toISOString(),
        source: 'clear'
      };
      
      // Validate before setting state and storage
      if (emptyCreditData.credits === 0 && 
          emptyCreditData.totalCredits === 0 &&
          emptyCreditData.usedCredits === 0) {
        setCredits(0);
        await AsyncStorage.setItem('ai_credits', JSON.stringify(emptyCreditData));
        console.log('[AICreditContext] Credits fully cleared');
      } else {
        throw new Error('Invalid empty credit data structure');
      }
    } catch (error) {
      console.error('Error clearing credits:', error);
      // Fallback to basic reset if validation fails
      setCredits(0);
      await AsyncStorage.setItem('ai_credits', JSON.stringify({ credits: 0 }));
    }
  };

  /**
   * Update credits from API data (called during login)
   * @param {Object} aiCreditsData - AI credits data from server
   */
  const updateCredits = async (aiCreditsData) => {
    try {
      console.log('[AICreditContext] ===== UPDATING CREDITS FROM API =====');
      console.log('[AICreditContext] Received AI credits data:', {
        totalCredits: aiCreditsData?.totalCredits || 0,
        usedCredits: aiCreditsData?.usedCredits || 0,
        availableCredits: aiCreditsData?.availableCredits || 0,
        transactionsCount: aiCreditsData?.transactions?.length || 0,
        creditsId: aiCreditsData?.creditsId
      });

      if (aiCreditsData) {
        // Validate and calculate available credits
        const total = Math.max(0, Number(aiCreditsData.totalCredits) || 0);
        const used = Math.max(0, Number(aiCreditsData.usedCredits) || 0);
        const available = Math.max(0, 
          Number.isFinite(aiCreditsData.availableCredits) 
            ? aiCreditsData.availableCredits 
            : total - used
        );

        setCredits(available);

        console.log('[AICreditContext] Credits updated from API:', {
          previousCredits: credits,
          newCredits: availableCredits,
          totalCredits: aiCreditsData.totalCredits,
          usedCredits: aiCreditsData.usedCredits
        });

        // Store updated credits in AsyncStorage
        const creditsData = {
          credits: availableCredits,
          totalCredits: aiCreditsData.totalCredits || 0,
          usedCredits: aiCreditsData.usedCredits || 0,
          transactions: aiCreditsData.transactions || [],
          lastUpdated: new Date().toISOString(),
          source: 'api_sync'
        };

        await AsyncStorage.setItem('ai_credits', JSON.stringify(creditsData));
        console.log('[AICreditContext] Credits data stored in AsyncStorage');
      } else {
        console.warn('[AICreditContext] No AI credits data provided');
      }

      console.log('[AICreditContext] ===== CREDITS UPDATE COMPLETE =====');
    } catch (error) {
      console.error('[AICreditContext] Error updating credits from API:', error);
    }
  };

  // Context value
  const value = {
    credits,
    loading,
    error,
    addCredits,
    useCredits,
    clearCredits,
    addPremiumCredits,
    updateCredits
  };

  return (
    <AICreditContext.Provider value={value}>
      {children}
    </AICreditContext.Provider>
  );
};

/**
 * Hook to use the AI credit context
 */
export const useAICredit = () => {
  const context = useContext(AICreditContext);
  if (context === undefined) {
    throw new Error('useAICredit must be used within an AICreditProvider');
  }
  return context;
};

export default AICreditContext;