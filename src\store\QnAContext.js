import React, { createContext, useContext, useEffect, useState, useCallback, useRef } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import apiClient from '../services/ApiClient';
import { usePurchase } from './PurchaseContext';
import { useExamContext } from './ExamContext';

const QnAContext = createContext();

export const QnAProvider = ({ children }) => {
    const { selectedExam } = useExamContext();
    const [currentExam, setCurrentExam] = useState(null);
    const [qnas, setQnas] = useState([]);
    const [questionsBySubject, setQuestionsBySubject] = useState({});
    const [isLoading, setIsLoading] = useState(false);
    const abortControllerRef = useRef(null);
    const pendingExamCodeRef = useRef(null);

    // Get purchase context to check if user has premium access
    const purchaseContext = usePurchase();
    const isSubscriptionActive = purchaseContext?.isSubscriptionActive || (() => false);
    const refreshPurchases = purchaseContext?.refreshPurchases;

    // Enhanced login state detection - use multiple sources to determine if user is logged in
    const contextIsLoggedIn = purchaseContext?.isLoggedIn || false;
    const contextUserId = purchaseContext?.userId;
    const contextUser = purchaseContext?.user;

    // A user is considered logged in if:
    // 1. The LoginContext says they're logged in, OR
    // 2. We have a valid userId in the PurchaseContext, OR
    // 3. We have a valid user object in the PurchaseContext
    const isLoggedIn = contextIsLoggedIn || !!contextUserId || !!contextUser?.id;

    /* console.log(`[QnAContext] Login state detection:`, {
        contextIsLoggedIn,
        hasUserId: !!contextUserId,
        hasUser: !!contextUser?.id,
        finalIsLoggedIn: isLoggedIn
    }); */

    useEffect(() => {
        // Check if qnas is an array before using reduce
        if (Array.isArray(qnas) && qnas.length > 0) {
            const grouped = qnas.reduce((acc, q) => {
                const subjectCode = q?.subject ? Number(q.subject) : 0;
                acc[subjectCode] = [...(acc[subjectCode] || []), q];
                return acc;
            }, {});
            setQuestionsBySubject(grouped);
        } else {
            // If qnas is not an array or is empty, set questionsBySubject to an empty object
            setQuestionsBySubject({});
        }
    }, [qnas]);

    useEffect(() => {
      const init = async () => {
        try {
          const stored = await AsyncStorage.getItem('qnas');
          if (stored) {
            const parsedData = JSON.parse(stored);
            // Ensure we have an array and filter out invalid items
            if (Array.isArray(parsedData)) {
              const parsed = parsedData.filter(q => !!q?.subject);
              setQnas(parsed);
            } else {
              console.log('Stored qnas data is not an array, initializing empty array');
              setQnas([]);
            }
          }
        } catch (error) {
          console.error('Error initializing qnas from storage:', error);
          setQnas([]);
        }
      };
      init();

      // Setup subscription status change listener
      window.subscriptionStatusChanged = (examCode, isActive) => {
        console.log(`[QnAContext] Subscription status changed for ${examCode}: ${isActive}`);
        if (selectedExam?.exam_code === examCode) {
          loadQnA(examCode, !isActive); // Load premium content if active, free if not
        }
      };

      return () => {
        // Clean up listener
        window.subscriptionStatusChanged = null;
      };
    }, [selectedExam]);

    // This effect is no longer needed as we're not using fullQnaSet and limitedQnaSet anymore
    // The premium/free user distinction is now handled directly in the loadQnA function

    const handleAbortPreviousRequest = () => {
        if (abortControllerRef.current) {
            abortControllerRef.current.abort();
            console.log('[LOGINSYNC] Aborted previous QnA request');
        }
    };

    const fetchQnAData = async (examCode, is_free, controller, productId) => {
        let hasPremiumAccess = false;

        const data = await apiClient.getQnA(examCode, { signal: controller.signal }, is_free);
        const validData = Array.isArray(data) ? data : [];
        setQnas(validData);
        await AsyncStorage.setItem('qnas', JSON.stringify(validData));
        setCurrentExam(examCode);
    };

    const loadQnA = async (examCode, is_free = true, productId = null) => {
        const requestId = Math.random().toString(36).substring(2, 8);
        console.log(`[LOGINSYNC] [${requestId}] ====== QNA LOADING STARTED ======`);
        console.log(`[LOGINSYNC] [${requestId}] Loading QnA for exam: ${examCode}, explicit is_free=${is_free}, productId=${productId}`);

        handleAbortPreviousRequest();

        const controller = new AbortController();
        abortControllerRef.current = controller;
        pendingExamCodeRef.current = examCode;
        setIsLoading(true);

        try {
            await fetchQnAData(examCode, is_free, controller, productId);
        } catch (error) {
            if (error.name !== 'AbortError') {
                console.error('[LOGINSYNC] QnA load failed:', error.message);
            }
        } finally {
            setIsLoading(false);
            pendingExamCodeRef.current = null;
            abortControllerRef.current = null;
        }
    };
    
    return (
        <QnAContext.Provider value={{
            currentExam,
            qnas,
            questionsBySubject,
            loadQnA,
            isLoading,
            pendingExamCodeRef // Expose the pending exam code ref for login/logout flows
        }}>
            {children}
        </QnAContext.Provider>
    );
};

export const useQnAContext = () => useContext(QnAContext);