import React, {
  useState,
  useEffect,
  useCallback,
} from 'react';
import { Alert, AppState } from 'react-native';
import NetInfo from '@react-native-community/netinfo';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {
  getFocusedRouteNameFromRoute,
  NavigationContainer,
} from '@react-navigation/native';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import {
  PaperProvider,
  useTheme,
} from 'react-native-paper';
import BootSplash from 'react-native-bootsplash';
import ProductSelect from './src/ProductSelect';
import RevenueCatTestScreen from './src/components/RevenueCatTestScreen';
import { AppProvider } from './src/store/AppContext';
import { ProductProvider } from './src/store/ProductContext';
import { QuizResultProvider } from './src/store/QuizResultContext';
import Home from './src/Home';
import { BottomNavigation } from 'react-native-paper';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { CommonActions } from '@react-navigation/native';

import {
  QuestionStackNavigator,
  QuizStackNavigator,
  SettingStackNavigator,
} from './src/StackNavigators';
import Entypo from 'react-native-vector-icons/Entypo';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import Feather from 'react-native-vector-icons/Feather';
import { FilterProvider } from './src/store/FilterContext';
import { QuizSettingProvider } from './src/store/QuizSettingContext';
import { PreferencesProvider, usePreferencesContext } from './src/store/PreferencesContext';
import { AIChatProvider } from './src/store/AIChatContext';
import { PurchaseProvider } from './src/store/PurchaseContext';
import { AICreditProvider } from './src/store/AICreditContext';
import { LoginProvider, useLogin } from './src/store/LoginContext';
import { CopilotProvider } from 'react-native-copilot';
import Purchases from 'react-native-purchases';
import { useSubscriptionRefresh } from './src/utils/subscriptionUtils';

const Tab = createBottomTabNavigator();

/* Purchases.addCustomerInfoUpdateListener(customerInfo => {
  console.log('renewal listener: RevenueCat subscription updated:', JSON.stringify(customerInfo, null, 2));
  //useSubscriptionRefresh(); //TODO not real time and invalid hook...
}); */

export default function New() {
  const [initialRoute, setInitialRoute] = useState(null);
  const [isConnected, setIsConnected] = useState(true);

  // Continuous network connectivity monitoring with alert management
  useEffect(() => {
    let isMounted = true;
    let lastAlertTime = 0;
    const ALERT_COOLDOWN = 1000; // 1 second between alerts

    const showConnectionAlert = async () => {
      const now = Date.now();
      if (now - lastAlertTime > ALERT_COOLDOWN) {
        Alert.alert(
          'No Internet Connection',
          'Please connect to the internet to continue using the app!',
          [{
            text: 'OK',
            onPress: async () => {
              // Reset the alert cooldown when user clicks OK to allow immediate re-checking
              lastAlertTime = 0;
              // Recheck connection when OK is pressed
              const state = await NetInfo.fetch();
              if (!state.isConnected) {
                // If still offline, show alert again immediately
                setTimeout(() => showConnectionAlert(), 100);
              }
            }
          }]
        );
        lastAlertTime = now;
      }
    };

    const handleConnectivityChange = (state) => {
      if (!isMounted) return;

      const connected = state.isConnected;
      setIsConnected(connected);

      if (!connected) {
        showConnectionAlert();
      }
    };

    // Check initial state
    NetInfo.fetch().then(handleConnectivityChange);

    // Subscribe to network changes
    const unsubscribeNetInfo = NetInfo.addEventListener(handleConnectivityChange);

    // Add AppState listener to check when app comes to foreground
    const subscription = AppState.addEventListener('change', (nextAppState) => {
      if (nextAppState === 'active') {
        NetInfo.fetch().then(state => {
          if (!state.isConnected) {
            showConnectionAlert();
          }
        });
      }
    });

    return () => {
      isMounted = false;
      unsubscribeNetInfo();
      subscription.remove();
    };
  }, []);

  const CustomTabBar = ({ navigation, state, descriptors, insets }) => {
    const handleTabPress = useCallback(
      ({ route, preventDefault }) => {
        const event = navigation.emit({
          type: 'tabPress',
          target: route.key,
          canPreventDefault: true,
        });

        if (event.defaultPrevented) {
          preventDefault();
        } else {
          navigation.dispatch({
            ...CommonActions.navigate(route.name, route.params),
            target: state.key,
          });
        }
      },
      [navigation, state],
    );

    const route = state.routes[state.index];
    const focusedRouteName = getFocusedRouteNameFromRoute(route) ?? route.name;

    const HIDDEN_TAB_SCREENS = ['QnADetail', 'QuizMode', 'QuizResult', 'ChatScreen'];

    // Conditionally hide tab bar
    if (HIDDEN_TAB_SCREENS.includes(focusedRouteName)) {
      return null;
    }

    return (
      <BottomNavigation.Bar
        navigationState={state}
        safeAreaInsets={insets}
        onTabPress={handleTabPress}
        renderIcon={({ route, focused, color }) =>
          descriptors[route.key].options.tabBarIcon?.({
            focused,
            color,
            size: 24,
          }) || null
        }
        getLabelText={({ route }) => descriptors[route.key].route.name}
      />
    );
  };

  const ThemedTabs = () => {
    const theme = useTheme();

    return (
      <Tab.Navigator
        screenOptions={{ headerShown: false }}
        tabBar={props => <CustomTabBar {...props} />}>
        <Tab.Screen
          name="Home"
          component={Home}
          options={{
            tabBarIcon: ({ color, size }) => (
              <Entypo name="home" size={size} color={color} />
            ),
          }}
        />
        <Tab.Screen
          name="Q&A"
          component={QuestionStackNavigator}
          options={{
            tabBarIcon: ({ color, size }) => (
              <MaterialIcons name="topic" size={size} color={color} />
            ),
          }}
        />
        <Tab.Screen
          name="Quiz"
          component={QuizStackNavigator}
          options={{
            tabBarIcon: ({ color, size }) => (
              <MaterialIcons name="quiz" size={size} color={color} />
            ),
          }}
        />
        <Tab.Screen
          name="Setting"
          component={SettingStackNavigator}
          options={{
            tabBarIcon: ({ color, size }) => (
              <Feather name="user" size={size} color={color} />
            ),
          }}
        />
      </Tab.Navigator>
    );
  };

  const RootStack = createNativeStackNavigator();

  const RootNavigator = () => (
    <RootStack.Navigator
      screenOptions={{ headerShown: false }}
      initialRouteName={initialRoute}
    >
      <RootStack.Screen name="MainTabs" component={ThemedTabs} />
      <RootStack.Screen
        name="ProductSelect"
        component={ProductSelect}
        options={{
          gestureEnabled: false, // Disable swipe back gesture
        }}
      />
      <RootStack.Screen name="QuestionStack" component={QuestionStackNavigator} />
      <RootStack.Screen name="QuizStack" component={QuizStackNavigator} />
      <RootStack.Screen name="SettingStack" component={SettingStackNavigator} />
      <RootStack.Screen name="Welcome2" component={ProductSelect} />
      <RootStack.Screen
        name="RevenueCatTest"
        component={RevenueCatTestScreen}
        options={{
          headerShown: true,
          title: 'RevenueCat SDK Test',
          headerBackTitle: 'Back'
        }}
      />
    </RootStack.Navigator>
  );

  const ThemeContainer = () => {
    const { theme, rippleEffectEnabled } = usePreferencesContext();
    const { initUser } = useLogin();

    useEffect(() => {
      const checkSelectedProduct = async () => {
        try {
          const selectedProduct = await AsyncStorage.getItem('selectedProduct');
          setInitialRoute(selectedProduct ? 'MainTabs' : 'ProductSelect');
        } catch (error) {
          console.error('Error checking product selection:', error);
          setInitialRoute('ProductSelect'); // Default to ProductSelect if error occurs
        }
      };

      (async () => {
        await initUser();
        await checkSelectedProduct();
        await BootSplash.hide({ fade: true });
        console.log('BootSplash has been hidden successfully');
      })();
    }, []);

    // Don't render navigation until we know the initial route
    if (initialRoute === null) {
      return null;
    }

    return (
      <PaperProvider settings={{ rippleEffectEnabled }} theme={theme}>
        <NavigationContainer theme={theme}>
          <RootNavigator />
        </NavigationContainer>
      </PaperProvider>
    );
  };

  return (
    <LoginProvider>
      <CopilotProvider>
        <AIChatProvider>
          <PreferencesProvider>
            <AppProvider>
              <PurchaseProvider>
                <AICreditProvider>
                  <QuizResultProvider>
                    <QuizSettingProvider>
                      <ProductProvider>
                        <FilterProvider>
                          <ThemeContainer />
                        </FilterProvider>
                      </ProductProvider>
                    </QuizSettingProvider>
                  </QuizResultProvider>
                </AICreditProvider>
              </PurchaseProvider>
            </AppProvider>
          </PreferencesProvider>
        </AIChatProvider>
      </CopilotProvider>
    </LoginProvider>
  );
}