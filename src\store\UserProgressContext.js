import React, { createContext, useContext, useEffect, useState } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import userProgressSyncService from '../services/UserProgressSyncService';

const UserProgressContext = createContext();

export const UserProgressProvider = ({ children }) => {
  const [progress, setProgress] = useState({});

  // Load progress on mount
  useEffect(() => {
    const loadProgress = async () => {
      const stored = await AsyncStorage.getItem('userProgress');
      if (stored) setProgress(JSON.parse(stored));
    };
    loadProgress();
  }, []);

  // Save progress whenever it changes
  useEffect(() => {
    const saveProgress = async () => {
      await AsyncStorage.setItem('userProgress', JSON.stringify(progress));
    };
    saveProgress();
  }, [progress]);

  // Unified function to save progress to AsyncStorage
  const saveProgress = async (newProgress) => {
    await AsyncStorage.setItem('userProgress', JSON.stringify(newProgress));
  };

  // Helper to normalize questionId to a string path
  const normalizeId = (questionId) =>
    Array.isArray(questionId) ? questionId.join('/') : questionId;

  // Split normalized ID into individual IDs using "///"
  const splitIds = (normalizedId) =>
    normalizedId.split('///').filter(id => id); // Filter to remove empty strings

  // Update browsed status with normalized IDs
  const updateProgress = (examId, subject, questionId) => {
    const normalizedId = normalizeId(questionId);
    const ids = splitIds(normalizedId);
    const timestamp = Date.now();
    setProgress(prev => {
      const examData = prev[examId] || {};
      const subjectData = examData[subject] || {
        browsed: [], bookmarked: [], incorrect: [], correct: []
      };

      const existingBrowsedIds = new Set(subjectData.browsed.map(b => b.id));
      const newBrowsedEntries = ids
        .filter(id => !existingBrowsedIds.has(id))
        .map(id => ({ id, timestamp }));

      if (newBrowsedEntries.length === 0) return prev;

      const newProgress = {
        ...prev,
        [examId]: {
          ...examData,
          [subject]: {
            ...subjectData,
            browsed: [...subjectData.browsed, ...newBrowsedEntries]
          }
        }
      };
      saveProgress(newProgress);

      // Sync each new browsed entry to the API
      newBrowsedEntries.forEach(entry => {
        userProgressSyncService.syncProgressUpdateDebounced(examId, subject, entry.id, 'browsed', {}, newProgress)
          .catch(error => {
            console.warn(`[UserProgressContext] Failed to sync browsed progress for ${examId}/${subject}/${entry.id}:`, error);
          });
      });

      return newProgress;
    });
  };
  const isBookmarked = (examId, subject, questionId) => {
    return progress[examId]?.[subject]?.bookmarked?.some(b => b.id === questionId);
  };

  // Toggle bookmark with normalized IDs
  const toggleBookmark = (examId, subject, questionId) => {
    const normalizedId = normalizeId(questionId);
    const ids = splitIds(normalizedId);
    const timestamp = Date.now();
    setProgress(prev => {
      const examData = prev[examId] || {};
      const subjectData = examData[subject] || {
        browsed: [], bookmarked: [], incorrect: [], correct: []
      };

      const bookmarkedArray = subjectData.bookmarked || [];
      const toAdd = [];
      const toRemove = new Set();

      ids.forEach(id => {
        const existingIndex = bookmarkedArray.findIndex(b => b.id === id);
        if (existingIndex === -1) {
          toAdd.push({ id, timestamp });
        } else {
          toRemove.add(id);
        }
      });

      const newBookmarked = bookmarkedArray
        .filter(b => !toRemove.has(b.id))
        .concat(toAdd);

      const newProgress = {
        ...prev,
        [examId]: {
          ...examData,
          [subject]: { ...subjectData, bookmarked: newBookmarked }
        }
      };
      saveProgress(newProgress);

      // Sync bookmark changes to the API
      ids.forEach(id => {
        const wasBookmarked = bookmarkedArray.some(b => b.id === id);
        const isNowBookmarked = newBookmarked.some(b => b.id === id);

        if (wasBookmarked !== isNowBookmarked) {
          userProgressSyncService.syncProgressUpdateDebounced(examId, subject, id, 'bookmarked', {
            isBookmarked: isNowBookmarked
          }, newProgress).catch(error => {
            console.warn(`[UserProgressContext] Failed to sync bookmark progress for ${examId}/${subject}/${id}:`, error);
          });
        }
      });

      return newProgress;
    });
  };

  // Mark incorrect: Remove from correct, add/update in incorrect
  const markIncorrect = (examId, subject, questionId) => {
    const normalizedId = normalizeId(questionId);
    const ids = splitIds(normalizedId);
    const timestamp = Date.now();
    setProgress(prev => {
      const examData = prev[examId] || {};
      const subjectData = examData[subject] || {
        browsed: [], bookmarked: [], incorrect: [], correct: []
      };

      // Remove all matching IDs from correct
      const newCorrect = (subjectData.correct || [])
        .filter(c => !ids.includes(c.id));

      // Add new entries for each ID with current timestamp
      const existingIncorrectIds = new Set((subjectData.incorrect || []).map(i => i.id));
      const newIncorrect = [
        ...(subjectData.incorrect || []).filter(i => !ids.includes(i.id)),
        ...ids.map(id => ({ id, timestamp }))
      ];

      const newProgress = {
        ...prev,
        [examId]: {
          ...examData,
          [subject]: { ...subjectData, correct: newCorrect, incorrect: newIncorrect }
        }
      };
      saveProgress(newProgress);

      // Sync answered (incorrect) progress to the API
      ids.forEach(id => {
        userProgressSyncService.syncProgressUpdateDebounced(examId, subject, id, 'answered', {
          isCorrect: false
        }, newProgress).catch(error => {
          console.warn(`[UserProgressContext] Failed to sync incorrect answer progress for ${examId}/${subject}/${id}:`, error);
        });
      });

      return newProgress;
    });
  };

  // Mark correct: Remove from incorrect, add/update in correct
  const removeFromIncorrect = (examId, subject, questionId) => {
    const normalizedId = normalizeId(questionId);
    const ids = splitIds(normalizedId);
    const timestamp = Date.now();
    setProgress(prev => {
      const examData = prev[examId] || {};
      const subjectData = examData[subject] || {
        browsed: [], bookmarked: [], incorrect: [], correct: []
      };

      // Remove all matching IDs from incorrect
      const newIncorrect = (subjectData.incorrect || [])
        .filter(i => !ids.includes(i.id));

      // Add new entries for each ID with current timestamp
      const existingCorrectIds = new Set((subjectData.correct || []).map(c => c.id));
      const newCorrect = [
        ...(subjectData.correct || []).filter(c => !ids.includes(c.id)),
        ...ids.map(id => ({ id, timestamp }))
      ];

      const newProgress = {
        ...prev,
        [examId]: {
          ...examData,
          [subject]: { ...subjectData, incorrect: newIncorrect, correct: newCorrect }
        }
      };
      saveProgress(newProgress);

      // Sync answered (correct) progress to the API
      ids.forEach(id => {
        userProgressSyncService.syncProgressUpdateDebounced(examId, subject, id, 'answered', {
          isCorrect: true
        }, newProgress).catch(error => {
          console.warn(`[UserProgressContext] Failed to sync correct answer progress for ${examId}/${subject}/${id}:`, error);
        });
      });

      return newProgress;
    });
  };

  // Batch process to mark multiple questions as correct across multiple subjects
  // subjectQuestionsMap is a Map where keys are subject IDs and values are arrays of question IDs
  const batchRemoveFromIncorrect = (examId, subjectQuestionsMap) => {
    const timestamp = Date.now();

    setProgress(prev => {
      const examData = prev[examId] || {};
      let newExamData = { ...examData };

      // Process each subject
      Object.entries(subjectQuestionsMap).forEach(([subject, questionIds]) => {
        // Skip if no questions for this subject
        if (!questionIds || questionIds.length === 0) return;

        const subjectData = examData[subject] || {
          browsed: [], bookmarked: [], incorrect: [], correct: []
        };

        // Process all question IDs for this subject
        const allIds = questionIds.flatMap(qId => {
          const normalizedId = normalizeId(qId);
          return splitIds(normalizedId);
        });

        // Remove all matching IDs from incorrect
        const newIncorrect = (subjectData.incorrect || [])
          .filter(i => !allIds.includes(i.id));

        // Add new entries for each ID with current timestamp
        const newCorrect = [
          ...(subjectData.correct || []).filter(c => !allIds.includes(c.id)),
          ...allIds.map(id => ({ id, timestamp }))
        ];

        // Update the subject data
        newExamData = {
          ...newExamData,
          [subject]: { ...subjectData, incorrect: newIncorrect, correct: newCorrect }
        };
      });

      const newProgress = {
        ...prev,
        [examId]: newExamData
      };

      saveProgress(newProgress);
      return newProgress;
    });
  };

  // Batch process to mark multiple questions as incorrect across multiple subjects
  // subjectQuestionsMap is a Map where keys are subject IDs and values are arrays of question IDs
  const batchMarkIncorrect = (examId, subjectQuestionsMap) => {
    const timestamp = Date.now();

    setProgress(prev => {
      const examData = prev[examId] || {};
      let newExamData = { ...examData };

      // Process each subject
      Object.entries(subjectQuestionsMap).forEach(([subject, questionIds]) => {
        // Skip if no questions for this subject
        if (!questionIds || questionIds.length === 0) return;

        const subjectData = examData[subject] || {
          browsed: [], bookmarked: [], incorrect: [], correct: []
        };

        // Process all question IDs for this subject
        const allIds = questionIds.flatMap(qId => {
          const normalizedId = normalizeId(qId);
          return splitIds(normalizedId);
        });

        // Remove all matching IDs from correct
        const newCorrect = (subjectData.correct || [])
          .filter(c => !allIds.includes(c.id));

        // Add new entries for each ID with current timestamp
        const newIncorrect = [
          ...(subjectData.incorrect || []).filter(i => !allIds.includes(i.id)),
          ...allIds.map(id => ({ id, timestamp }))
        ];

        // Update the subject data
        newExamData = {
          ...newExamData,
          [subject]: { ...subjectData, correct: newCorrect, incorrect: newIncorrect }
        };
      });

      const newProgress = {
        ...prev,
        [examId]: newExamData
      };

      saveProgress(newProgress);
      return newProgress;
    });
  };

  // Clear all progress data and sync state (for logout)
  const clearProgress = () => {
    setProgress({});
    userProgressSyncService.clearState();
    AsyncStorage.removeItem('userProgress');
    console.log('[UserProgressContext] Progress data and sync state cleared');
  };

  // Enable/disable sync
  const setSyncEnabled = (enabled) => {
    userProgressSyncService.setSyncEnabled(enabled);
    console.log(`[UserProgressContext] Progress sync ${enabled ? 'enabled' : 'disabled'}`);
  };

  return (
    <UserProgressContext.Provider value={{
      progress,
      updateProgress,
      toggleBookmark,
      markIncorrect,
      removeFromIncorrect,
      batchMarkIncorrect,
      batchRemoveFromIncorrect,
      isBookmarked,
      clearProgress,
      setSyncEnabled
    }}>
      {children}
    </UserProgressContext.Provider>
  );
};

export const useUserProgress = () => useContext(UserProgressContext);