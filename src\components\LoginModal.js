import React, { useState, useEffect } from 'react';
import { View, StyleSheet, Alert, ActivityIndicator } from 'react-native';
import { Modal, Portal, Text, Button, useTheme, Snackbar } from 'react-native-paper';
import { GoogleSignin, GoogleSigninButton, statusCodes } from '@react-native-google-signin/google-signin';
import AsyncStorage from '@react-native-async-storage/async-storage';

import AuthService from '../services/AuthService';
import { useLogin } from '../store/LoginContext';
import { usePurchase } from '../store/PurchaseContext';
import { useExamContext } from '../store/ExamContext';
import { useQnAContext } from '../store/QnAContext';
import { handlePostLoginFlow } from '../utils/loginUtils';

/**
 * A modal component that displays a Google Sign-In button
 * and handles the login process.
 *
 * @param {boolean} visible - Whether the modal is visible
 * @param {function} onDismiss - Function to call when the modal is dismissed
 * @param {function} onLoginSuccess - Function to call when login is successful
 * @param {string} source - Source of the login modal ('aiCredit', 'aiChat', 'buyNow')
 * @param {object} navigation - Navigation object for screen transitions (required for buyNow source)
 * @param {object} targetExam - Target exam for purchase (required for buyNow source)
 * @param {function} setBuyNowModalVisible - Function to control BuyNow modal visibility (required for buyNow source)
 */
const LoginModal = ({
  visible,
  onDismiss,
  onLoginSuccess,
  source = 'default',
  navigation = null,
  targetExam = null,
  setBuyNowModalVisible = null
}) => {
  const { colors } = useTheme();
  const [loading, setLoading] = useState(false);
  const [snackbarVisible, setSnackbarVisible] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');
  const [snackbarType, setSnackbarType] = useState('success'); // 'success' or 'error'

  // Get login context
  const loginContext = useLogin();

  // Get purchase context for user authentication
  const { isSubscriptionActive, updateUser, storeSubscriptionStatus } = usePurchase();

  // Get exam context to access the selected exam
  const { selectedExam } = useExamContext();

  // Get QnA context to reload questions
  const { loadQnA } = useQnAContext();

  const handleLogin = async () => {
    try {
      setLoading(true);

      // Use LoginContext if available
      if (loginContext && loginContext.login) {
        console.log('[LoginModal] Using LoginContext for login');
        const result = await loginContext.login();

        // Handle login cancellation
        if (result.cancelled) {
          console.log('[LoginModal] Login was cancelled by user');
          setSnackbarMessage('Sign-in was cancelled');
          setSnackbarType('info');
          setSnackbarVisible(true);
          onDismiss();
          return;
        }

        // Handle login failure
        if (!result.success) {
          console.error('[LoginModal] Login failed:', result.error);
          setSnackbarMessage(`Login failed: ${result.error}`);
          setSnackbarType('error');
          setSnackbarVisible(true);
          onDismiss();
          return;
        }

        // Login was successful
        console.log('[LoginModal] Login successful');

        // Get the user ID from LoginContext
        const userId = loginContext.user?.id;

        // Use the optimized post-login flow with explicit userId
        /* await handlePostLoginFlow({
          selectedExam,
          isSubscriptionActive,
          loadQnA,
          updateUser, // Use updateUser instead of refreshPurchases for better performance
          userId: userId,
          onComplete: ({ success, hasPremiumAccess }) => {
            console.log(`[LOGINSYNC] Login completed. Success: ${success}, Premium: ${hasPremiumAccess}`);
          },
          delayMs: 100 // Use minimal delay for better performance
        }); */

        // Handle different behaviors based on source
        if (source === 'aiChat') {
          // For AI Chat, just call the success callback without showing a snackbar
          if (onLoginSuccess) {
            onLoginSuccess(loginContext.user);
          }
          // Dismiss the modal without showing a snackbar
          onDismiss();
        }
        else if (source === 'buyNow' && targetExam && setBuyNowModalVisible && navigation) {
          // For BuyNow modal, check if user already has premium access
          const hasPremiumAccess = await isSubscriptionActive(targetExam.exam_code, true) || false;

          if (hasPremiumAccess) {
            console.log(`[LoginModal] User already has premium access to ${targetExam.exam_code}`);

            // 1. Close both modals
            onDismiss(); // Close login modal
            setBuyNowModalVisible(false); // Close BuyNow modal

            // 2. Load premium content
            //await loadQnA(targetExam.exam_code, false, targetExam.id);
            await storeSubscriptionStatus(targetExam.exam_code, true);

            // 3. Navigate to Home
            if (navigation) {
              navigation.reset({
                index: 0,
                routes: [{ name: 'MainTabs' }],
              });
            }

            // 4. Show alert
            setTimeout(() => {
              Alert.alert(
                'Premium Access',
                'You already have premium access to this exam',
                [{ text: 'OK' }]
              );
            }, 300);
          } else {
            // User doesn't have premium access, just call success callback
            if (onLoginSuccess) {
              onLoginSuccess(loginContext.user);
            }
            // Dismiss the login modal
            onDismiss();
          }
        }
        else {
          // Default behavior (including aiCredit)
          // Call the success callback
          if (onLoginSuccess) {
            onLoginSuccess(loginContext.user);
          }

          // Show success snackbar
          setSnackbarMessage(`Logged in as ${loginContext.userProfile?.name || 'User'}`);
          setSnackbarType('success');
          setSnackbarVisible(true);

          // Dismiss the modal
          onDismiss();
        }

        return;
      }
    } finally {
      setLoading(false);
    }
  };

  const onDismissSnackbar = () => {
    setSnackbarVisible(false);
  };

  // Effect to show snackbar even after modal is dismissed
  useEffect(() => {
    return () => {
      // If component is unmounted with a pending snackbar message, show it globally
      if (snackbarMessage && !snackbarVisible) {
        // This would ideally use a global snackbar system
        console.log('Snackbar message on unmount:', snackbarMessage);
      }
    };
  }, [snackbarMessage, snackbarVisible]);

  return (
    <Portal>
      <Modal
        visible={visible}
        onDismiss={onDismiss}
        contentContainerStyle={[
          styles.container,
          { backgroundColor: colors.surface }
        ]}
      >
        <Text style={[styles.title, { color: colors.onSurfaceVariant }]}>
          Login Required
        </Text>

        <Text style={[styles.description, { color: colors.onSurfaceVariant }]}>
          Please log in with your Google account to continue.
        </Text>

        {loading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={colors.primary} />
          </View>
        ) : (
          <View style={styles.buttonContainer}>
            <GoogleSigninButton
              style={styles.googleButton}
              size={GoogleSigninButton.Size.Wide}
              color={GoogleSigninButton.Color.Dark}
              onPress={handleLogin}
            />
          </View>
        )}

        <Button
          mode="outlined"
          onPress={onDismiss}
          style={[styles.cancelButton, { borderColor: colors.outline }]}
          labelStyle={{ color: colors.primary }}
        >
          Cancel
        </Button>
      </Modal>

      <Snackbar
        visible={snackbarVisible}
        onDismiss={onDismissSnackbar}
        duration={3000}
        style={{
          backgroundColor: snackbarType === 'success' ? '#4CAF50' : '#F44336'
        }}
        action={{
          label: 'OK',
          onPress: onDismissSnackbar,
        }}
      >
        {snackbarMessage}
      </Snackbar>
    </Portal>
  );
};

const styles = StyleSheet.create({
  container: {
    margin: 20,
    padding: 24,
    borderRadius: 16,
    alignItems: 'center',
  },
  title: {
    fontSize: 22,
    fontWeight: 'bold',
    marginBottom: 16,
    textAlign: 'center',
  },
  description: {
    fontSize: 16,
    marginBottom: 24,
    textAlign: 'center',
    lineHeight: 22,
  },
  buttonContainer: {
    alignItems: 'center',
    marginBottom: 16,
  },
  googleButton: {
    width: 240,
    height: 48,
  },
  cancelButton: {
    marginTop: 8,
    width: '100%',
  },
  loadingContainer: {
    marginVertical: 24,
  },
});

export default LoginModal;
