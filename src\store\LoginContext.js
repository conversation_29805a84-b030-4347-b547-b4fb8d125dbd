import React, { createContext, useContext, useState, useEffect, useCallback, useRef } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { GoogleSignin, statusCodes } from '@react-native-google-signin/google-signin';

import { notifyLoginStateChange, clearUserData } from '../utils/loginUtils';
import RevenueCatService from '../services/RevenueCatService';

import AuthService from '../services/AuthService';

const LoginContext = createContext();

export const LoginProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [userProfile, setUserProfile] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isInitialized, setIsInitialized] = useState(false);
/* 
  // Ref to store the token refresh interval
  const tokenRefreshIntervalRef = useRef(null); */

  // Initialize GoogleSignin configuration
  useEffect(() => {
    try {
      AuthService.configureGoogleSignIn()
      console.log('[LoginContext] GoogleSignin configured successfully');
    } catch (error) {
      console.error('[LoginContext] Error configuring GoogleSignin:', error);
    }
  }, []);

  // Initialize login state from storage
  /* useEffect(() => {
    const initLoginState = async () => {
      try {
        setIsLoading(true);

        // Check AsyncStorage first (faster than network requests)
        const [storedUser, storedProfile] = await Promise.all([
          AsyncStorage.getItem('user'),
          AsyncStorage.getItem('apires')
        ]);

        if (storedUser) {
          setUser(JSON.parse(storedUser));
        }

        if (storedProfile) {
          setUserProfile(JSON.parse(storedProfile));
        }

        // Only check with Google if we have a stored user (to avoid unnecessary network requests)
        if (storedUser) {
          try {
            // Check if the isSignedIn method exists before calling it
            if (typeof GoogleSignin.isSignedIn === 'function') {
              // This is a lightweight check that doesn't require network if the token is cached
              const isSignedIn = await GoogleSignin.isSignedIn();

              if (!isSignedIn) {
                // If Google says we're not signed in but we have stored credentials,
                // clear the stored credentials to stay in sync
                console.log('Stored user exists but Google says not signed in, clearing local data');
                await AsyncStorage.multiRemove(['user', 'apires']);
                setUser(null);
                setUserProfile(null);
              }
            } else {
              console.log('[LoginContext] GoogleSignin.isSignedIn is not available, skipping check');
            }
          } catch (googleError) {
            console.log('Error checking Google sign-in status:', googleError);
            // Don't clear user data on error - just keep what we have from storage
          }
        }
      } catch (error) {
        console.error('Error initializing login state:', error);
      } finally {
        setIsLoading(false);
        setIsInitialized(true);
      }
    };

    initLoginState();
  }, []); */

  // Common function to process user data
  const _processUserData = useCallback(async (userInfo) => {
    // Get the ID token from the userInfo - check multiple possible locations
    const idToken = userInfo.idToken ||
                   (userInfo.serverAuthCode ? await GoogleSignin.getTokens().then(tokens => tokens.idToken) : null) ||
                   (userInfo.data && userInfo.data.idToken);

    console.log('[LoginContext] Google ID Token:', idToken ? `Found (length: ${idToken.length})` : 'Not found');

    // Skip backend authentication - use Google Sign-In directly
    if (!idToken) {
      console.error('[LoginContext] No ID token received from Google Sign-In');
      throw new Error('NO_ID_TOKEN');
    }

    // Extract user data based on the structure
    const userData = userInfo.data && userInfo.data.user ? userInfo.data.user : userInfo.user;

    // Check if we have valid user data
    if (!userData) {
      console.error('[LoginContext] Invalid user data received:', userInfo);
      return { success: false, cancelled: false, error: 'Invalid user data received' };
    }

    // Check if we have all required fields
    if (userData.id && userData.name && userData.email) {
      // Store user data
      const userJson = JSON.stringify(userData);
      await AsyncStorage.setItem('user', userJson);
      setUser(userData);

      // Store the token if available
      if (userInfo.idToken) {
        await AsyncStorage.setItem('google_id_token', userInfo.idToken);
        console.log('[LoginContext] Stored Google ID token in AsyncStorage');
      }

      // Create profile data
      const profileData = {
        name: userData.name || '',
        email: userData.email || '',
        profile_image: userData.photo || ''
      };

      // Store profile data
      const profileJson = JSON.stringify(profileData);
      await AsyncStorage.setItem('apires', profileJson);
      setUserProfile(profileData);

      // Notify about login state change
      await notifyLoginStateChanged(true);

      // Initialize RevenueCat service with Google user ID
      try {
        console.log('[LoginContext] Initializing RevenueCat service with Google user ID:', userData.id);
        await RevenueCatService.initialize(userData.id);
        console.log('[LoginContext] RevenueCat service initialized successfully');
      } catch (error) {
        console.error('[LoginContext] Failed to initialize RevenueCat service:', error);
        // Don't fail if RevenueCat initialization fails
      }

      return { success: true, userData };
    } else {
      console.error('[LoginContext] Incomplete user data:', userData);
      return { success: false, cancelled: false, error: 'Incomplete user data received from Google Sign-In' };
    }
  }, [notifyLoginStateChanged]);

  // Login function
  const login = useCallback(async () => {
    try {
      setIsLoading(true);

      // Check for Google Play Services
      await GoogleSignin.hasPlayServices({ showPlayServicesUpdateDialog: true });

      // Attempt to sign in
      //const userInfo = await GoogleSignin.signIn();
      const loginResult = await AuthService.loginWithGoogle();
      const userInfo = loginResult.userInfo;

      console.log('[LoginContext] Google Sign-In result:', JSON.stringify(loginResult, null, 2));

      // Check if user cancelled the sign-in
      if (userInfo.type === 'cancelled' || userInfo.type === 'cancel') {
        console.log('[LoginContext] User cancelled the login process');
        throw new Error('SIGN_IN_CANCELLED');
      }

      // Check for cancellation (safely check various formats)
      if (userInfo) {
        if (userInfo.type === 'cancelled' ||
            (userInfo.error && userInfo.error.includes && userInfo.error.includes('cancelled')) ||
            (userInfo.message && userInfo.message.includes && userInfo.message.includes('cancelled'))) {
          console.log('[LoginContext] User cancelled the login process');
          setIsLoading(false); // Make sure to reset loading state
          return { success: false, cancelled: true, error: 'User cancelled login' };
        }
      }

      return await _processUserData(userInfo);
    } catch (error) {
      console.error('[LoginContext] Login error:', error);

      // Handle specific error codes
      if (error.code === statusCodes.SIGN_IN_CANCELLED) {
        console.log('[LoginContext] User cancelled the login process (error code)');
        setIsLoading(false); // Make sure to reset loading state
        return { success: false, cancelled: true, error: 'User cancelled login' };
      } else if (error.code === 12501) { // Another cancellation code
        console.log('[LoginContext] User cancelled the login process (code 12501)');
        setIsLoading(false); // Make sure to reset loading state
        return { success: false, cancelled: true, error: 'User cancelled login' };
      } else if (error.message && error.message.includes('cancelled')) {
        console.log('[LoginContext] User cancelled the login process (message)');
        setIsLoading(false); // Make sure to reset loading state
        return { success: false, cancelled: true, error: 'User cancelled login' };
      } else if (error.code === statusCodes.IN_PROGRESS) {
        console.log('[LoginContext] Login already in progress');
        setIsLoading(false); // Make sure to reset loading state
        return { success: false, cancelled: false, error: 'Login already in progress' };
      } else if (error.code === statusCodes.PLAY_SERVICES_NOT_AVAILABLE) {
        console.log('[LoginContext] Google Play Services not available');
        setIsLoading(false); // Make sure to reset loading state
        return { success: false, cancelled: false, error: 'Google Play Services not available' };
      } else if (error.code === 'DEVELOPER_ERROR' || (error.message && error.message.includes('DEVELOPER_ERROR'))) {
        console.error('[LoginContext] DEVELOPER_ERROR in Google Sign-In');

        // Log additional details for debugging
        console.error('[LoginContext] DEVELOPER_ERROR details:', {
          message: error.message,
          code: error.code,
          stack: error.stack
        });

        setIsLoading(false); // Make sure to reset loading state
        return { success: false, cancelled: false, error: 'Google Sign-In configuration error. Please try again later or contact support.' };
      }

      setIsLoading(false); // Make sure to reset loading state
      return { success: false, cancelled: false, error: error.message || 'Unknown login error' };
    } finally {
      setIsLoading(false);
    }
  }, [notifyLoginStateChanged, _processUserData]);

  // Initialize user function (gets current user if signed in)
  const initUser = useCallback(async () => {
    try {
      setIsLoading(true);
      
      AuthService.configureGoogleSignIn();

      // Get current user info
      const userInfo = await GoogleSignin.getCurrentUser();
      console.log('[LoginContext] Current user info:', JSON.stringify(userInfo, null, 2));

      if (!userInfo) {
        console.log('[LoginContext] No current user found');
        await RevenueCatService.initialize();
        return { success: false, error: 'No current user' };
      }

      return await _processUserData(userInfo);
    } catch (error) {
      console.error('[LoginContext] Login error:', error);

      // Handle specific error codes
      if (error.code === statusCodes.SIGN_IN_CANCELLED) {
        console.log('[LoginContext] User cancelled the login process (error code)');
        setIsLoading(false); // Make sure to reset loading state
        return { success: false, cancelled: true, error: 'User cancelled login' };
      } else if (error.code === 12501) { // Another cancellation code
        console.log('[LoginContext] User cancelled the login process (code 12501)');
        setIsLoading(false); // Make sure to reset loading state
        return { success: false, cancelled: true, error: 'User cancelled login' };
      } else if (error.message && error.message.includes('cancelled')) {
        console.log('[LoginContext] User cancelled the login process (message)');
        setIsLoading(false); // Make sure to reset loading state
        return { success: false, cancelled: true, error: 'User cancelled login' };
      } else if (error.code === statusCodes.IN_PROGRESS) {
        console.log('[LoginContext] Login already in progress');
        setIsLoading(false); // Make sure to reset loading state
        return { success: false, cancelled: false, error: 'Login already in progress' };
      } else if (error.code === statusCodes.PLAY_SERVICES_NOT_AVAILABLE) {
        console.log('[LoginContext] Google Play Services not available');
        setIsLoading(false); // Make sure to reset loading state
        return { success: false, cancelled: false, error: 'Google Play Services not available' };
      } else if (error.code === 'DEVELOPER_ERROR' || (error.message && error.message.includes('DEVELOPER_ERROR'))) {
        console.error('[LoginContext] DEVELOPER_ERROR in Google Sign-In');

        // Log additional details for debugging
        console.error('[LoginContext] DEVELOPER_ERROR details:', {
          message: error.message,
          code: error.code,
          stack: error.stack
        });

        setIsLoading(false); // Make sure to reset loading state
        return { success: false, cancelled: false, error: 'Google Sign-In configuration error. Please try again later or contact support.' };
      }

      setIsLoading(false); // Make sure to reset loading state
      return { success: false, cancelled: false, error: error.message || 'Unknown login error' };
    } finally {
      setIsLoading(false);
    }
  }, [notifyLoginStateChanged]);

  // Logout function
  const logout = useCallback(async () => {
    try {
      setIsLoading(true);

      // Get user ID before clearing storage
      const userId = user?.id;

      // Sign out from Google
      await GoogleSignin.signOut();

      // Clear user data from AsyncStorage using centralized utility
      await clearUserData(userId);

      // Reset state
      setUser(null);
      setUserProfile(null);

      // Also clear the Google ID token
      await AsyncStorage.removeItem('google_id_token');
      console.log('[LoginContext] Cleared Google ID token from AsyncStorage');

      // Notify about login state change
      await notifyLoginStateChanged(false);

      return true;
    } catch (error) {
      console.error('Logout error:', error);
      return false;
    } finally {
      setIsLoading(false);
    }
  }, [notifyLoginStateChanged]);

  // Check if user is logged in
  const [isLoggedIn, setIsLoggedIn] = useState(false);

  useEffect(() => {
    const checkGoogleSignIn = async () => {
      try {
        const userInfo = await GoogleSignin.getCurrentUser();
        console.log('[LoginContext] checkGoogleSignIn:', !!userInfo);
        setIsLoggedIn(!!userInfo);
      } catch (error) {
        console.error('Error checking Google Sign-In status:', error);
        setIsLoggedIn(false);
      }
    };

    checkGoogleSignIn();
  }, [user]);

  // Method to notify listeners about login state changes
  const notifyLoginStateChanged = useCallback(async (isLoggedIn) => {
    try {
      console.log('Login state changed:', isLoggedIn ? 'Logged in' : 'Logged out');

      // Use the centralized utility function
      const userData = isLoggedIn ? user : null;
      await notifyLoginStateChange(isLoggedIn, userData);
    } catch (error) {
      console.error('Error storing login state change:', error);
    }
  }, [user]);

  // Method to directly update user data
  const updateUserData = useCallback(async (userData) => {
    try {
      console.log('[LoginContext] Directly updating user data:', userData.id);

      if (!userData || !userData.id) {
        console.error('[LoginContext] Invalid user data provided to updateUserData');
        return false;
      }

      // Update user state
      setUser(userData);

      // Create and update profile data
      const profileData = {
        name: userData.name || '',
        email: userData.email || '',
        profile_image: userData.photo || ''
      };

      setUserProfile(profileData);

      // Notify about login state change
      await notifyLoginStateChanged(true);

      return true;
    } catch (error) {
      console.error('[LoginContext] Error updating user data:', error);
      return false;
    }
  }, [notifyLoginStateChanged]);

  return (
    <LoginContext.Provider
      value={{
        user,
        userProfile,
        isLoading,
        isLoggedIn,
        isInitialized,
        initUser,
        login,
        logout,
        notifyLoginStateChanged,
        updateUserData,
      }}
    >
      {children}
    </LoginContext.Provider>
  );
};

export const useLogin = () => {
  const context = useContext(LoginContext);
  if (!context) {
    throw new Error('useLogin must be used within a LoginProvider');
  }
  return context;
};

// Helper to check if provider exists
export const useHasLoginProvider = () => {
  const context = useContext(LoginContext);
  return !!context;
};