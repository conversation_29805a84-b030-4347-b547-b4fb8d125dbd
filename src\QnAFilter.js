import React, { useState, useMemo, useEffect } from 'react';
import { View, ScrollView, StyleSheet, TouchableOpacity } from 'react-native';
import {
  Text,
  Appbar,
  useTheme,
  Chip,
  Button,
  Portal,
  Modal,
} from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { useNavigation } from '@react-navigation/native';
import CalendarPicker from 'react-native-calendar-picker';
import { dummyData } from './QnAList';
import { useFilters } from './store/FilterContext';
import DateCalendarInput from '../src/components/DateCalendarInput';
import { useExamContext } from './store/ExamContext';
import CustomAppbarContent from './components/CustomAppbarContent';


const QnAFilter = () => {
  const navigation = useNavigation();
  const { colors } = useTheme();
  const { appliedFilters, setAppliedFilters } = useFilters();

  const [selectedSubjects, setSelectedSubjects] = useState([]);
  /* const [selectedStartDate, setSelectedStartDate] = useState(null);
  const [selectedEndDate, setSelectedEndDate] = useState(null); */

  const { selectedExam } = useExamContext();
  const uniqueSubjects = useMemo(() => {
    return selectedExam?.subjects?.map((subject, index) => {
      if (typeof subject === 'object') {
        return {
          name: subject.name,
          code: Number(subject.code) || index + 1, // Ensure numeric code
        };
      }
      return {
        name: String(subject),
        code: index + 1, // Use index as numeric code for non-object subjects
      };
    }) || [];
  }, [selectedExam?.subjects]);
  



  // Add debug logs
  console.log('Available Subjects:', uniqueSubjects);
  console.log('Selected Subjects:', selectedSubjects);
  console.log('appliedFilters:', appliedFilters);

  // Sync subjects filter
  useEffect(() => {
    const allSubjectCodes = uniqueSubjects.map(subject => subject.code);
    const appliedSubjects = appliedFilters.subjects?.length > 0
      ? appliedFilters.subjects
      : allSubjectCodes;
    setSelectedSubjects(appliedSubjects);
    console.log(appliedFilters)
  }, [appliedFilters.subjects, uniqueSubjects]);

  // Update the applyFilters function
  const applyFilters = () => {
    const allSubjectCodes = uniqueSubjects.map(subject => subject.code);
    const finalSubjects = selectedSubjects.length === 0 
      ? allSubjectCodes 
      : selectedSubjects;
    
    setAppliedFilters({
      subjects: finalSubjects,
      /* dateRange: { startDate: selectedStartDate, endDate: selectedEndDate } */
    });
    navigation.goBack();
  };
  

  const toggleSubject = (index) => {
    setSelectedSubjects(prev => prev.includes(index)
      ? prev.filter(s => s !== index)
      : [...prev, index]
    );
  };

  /* const onDateChange = (date, type) => {
    type === 'START_DATE' ? setSelectedStartDate(date) : setSelectedEndDate(date);
  }; */


  /* const applyFilters = () => {
    // Generate all possible subject codes based on index
    const allSubjectCodes = uniqueSubjects.map((_, index) => index + 1);
 
    // Use all subjects if none are selected
    const finalSubjects = selectedSubjects.length === 0
      ? allSubjectCodes
      : selectedSubjects;
 
    setAppliedFilters({
      subjects: finalSubjects.map(code => parseInt(code, 10)),
      dateRange: { startDate: selectedStartDate, endDate: selectedEndDate }
    });
    navigation.goBack();
  }; 

  const formatDate = (date) => date ? date.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
  }) : 'Select Date';

  const handleResetDates = () => {
    setSelectedStartDate(null);
    setSelectedEndDate(null);
  };*/

  return (
    <View style={{ flex: 1, backgroundColor: colors.background }}>
      <Appbar.Header elevated>
        <Appbar.BackAction onPress={applyFilters} />
        {/* <Appbar.BackAction onPress={() => navigation.goBack()} /> */}
        <CustomAppbarContent title="Filter Options" />
        {/* <Appbar.Action icon="check" onPress={applyFilters} /> */}
        {/* <Appbar.Action icon="autorenew" onPress={handleReset} /> */}
      </Appbar.Header>

      <ScrollView contentContainerStyle={styles.container}>
        {/* Subjects Section */}
        <View style={styles.section}>
          <Text variant="titleSmall" style={[styles.sectionTitle, { color: colors.onSurface }]}>
            SELECT SUBJECTS
          </Text>
          <View style={styles.chipContainer}>
            {uniqueSubjects.map((subject, index) => (
              <TouchableOpacity
                key={subject.code}
                style={[
                  styles.subjectItem,
                  {
                    backgroundColor: colors.surface,
                    borderColor: selectedSubjects.includes(index + 1)
                      ? colors.primary
                      : colors.outlineVariant
                  }
                ]}
                onPress={() => toggleSubject(subject.code)}
              >
                <Text
                  style={[
                    styles.subjectText,
                    {
                      color: selectedSubjects.includes(index + 1)
                        ? colors.primary
                        : colors.onSurface
                    }
                  ]}
                >
                  {subject.name} {/* Changed from {subject} to {subject.name} */}
                </Text>
                <Icon
                  name={selectedSubjects.includes(index + 1)
                    ? "checkbox-marked"
                    : "checkbox-blank-outline"}
                  size={24}
                  color={selectedSubjects.includes(index + 1)
                    ? colors.primary
                    : colors.outline}
                />
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Date Range Section */}
        {/* <View style={styles.section}>
          <DateCalendarInput
            startDate={selectedStartDate}
            endDate={selectedEndDate}
            onDateChange={onDateChange}
          />
        </View> */}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 16,
    paddingBottom: 16,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    letterSpacing: 0.5,
    marginBottom: 12,
  },
  chipContainer: {
    gap: 8,
  },
  subjectItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderRadius: 8,
    borderWidth: 1,
    padding: 16,
  },
  subjectText: {
    flex: 1,
    marginRight: 12,
  },
});


export default QnAFilter;