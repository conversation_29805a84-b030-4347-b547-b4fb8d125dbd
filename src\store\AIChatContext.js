import React, { createContext, useContext, useEffect, useState } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import apiClient from '../services/ApiClient';

const AIChatContext = createContext();

export const AIChatProvider = ({ children }) => {
  const [chatHistories, setChatHistories] = useState({});

  // Load all chat histories on mount
  useEffect(() => {
    const loadChatHistories = async () => {
      try {
        const stored = await AsyncStorage.getItem('aiChatHistories');
        if (stored) {
          const parsed = JSON.parse(stored);
          setChatHistories(parsed);
          console.log('Loaded chat histories:', Object.keys(parsed).length);
        }
      } catch (error) {
        console.error('Failed to load chat histories:', error);
      }
    };
    loadChatHistories();
  }, []);

  // Save chat histories whenever they change
  useEffect(() => {
    const saveChatHistories = async () => {
      try {
        await AsyncStorage.setItem('aiChatHistories', JSON.stringify(chatHistories));
        //console.log('Saved chat histories:', Object.keys(chatHistories).length);
      } catch (error) {
        console.error('Failed to save chat histories:', error);
      }
    };

    if (Object.keys(chatHistories).length > 0) {
      saveChatHistories();
    }
  }, [chatHistories]);

  // Get chat history for specific Q&A
  const getChatHistory = (examCode, qnaId) => {
    console.log('[AIChatContext] ===== GETTING CHAT HISTORY =====');
    console.log('[AIChatContext] Request for examCode:', examCode, 'qnaId:', qnaId);

    if (!examCode || !qnaId) {
      console.warn('[AIChatContext] getChatHistory called with invalid params:', { examCode, qnaId });
      return defaultChatState();
    }

    console.log('[AIChatContext] Current chatHistories structure:', {
      totalExams: Object.keys(chatHistories).length,
      examCodes: Object.keys(chatHistories),
      requestedExamExists: !!chatHistories[examCode]
    });

    const savedHistory = chatHistories[examCode]?.[qnaId];

    if (chatHistories[examCode]) {
      console.log('[AIChatContext] Exam data for', examCode, ':', {
        totalQnAs: Object.keys(chatHistories[examCode]).length,
        qnaIds: Object.keys(chatHistories[examCode]),
        requestedQnAExists: !!savedHistory
      });
    }

    // If there's no saved history, return default state
    if (!savedHistory) {
      console.log('[AIChatContext] No saved history found, returning default state');
      return defaultChatState();
    }

    /* console.log('[AIChatContext] Found saved history:', {
      examCode,
      qnaId,
      messagesCount: savedHistory.messages?.length || 0,
      quickRepliesCount: savedHistory.quickReplies?.length || 0,
      firstMessage: savedHistory.messages?.[0]?.choices?.[0]?.message?.content?.substring(0, 50) + '...',
      isDefaultWelcome: savedHistory.messages?.[0]?.id === 'welcome-message'
    }); */

    // Log the entire message thread for this QnA
    /* console.log('[AIChatContext] ===== COMPLETE MESSAGE THREAD =====');
    console.log('[AIChatContext] chatHistories:',chatHistories);
    console.log('[AIChatContext] Exam:', examCode, 'QnA:', qnaId);
    console.log('[AIChatContext] Total Messages:', savedHistory.messages?.length || 0); */

    if (savedHistory.messages && savedHistory.messages.length > 0) {
      savedHistory.messages.forEach((message, index) => {
        const choice = message.choices?.[0];
        const messageContent = choice?.message;

        /* console.log(`[AIChatContext] Message ${index + 1}:`, {
          id: message.id,
          role: messageContent?.role,
          content: messageContent?.content?.substring(0, 100) + (messageContent?.content?.length > 100 ? '...' : ''),
          isLoading: messageContent?.isLoading,
          timestamp: message.timestamp || 'no timestamp',
          hasChoices: message.choices?.length || 0
        }); */

        // Log full content for debugging (truncated for readability)
        if (messageContent?.content) {
          //console.log(`[AIChatContext] Message ${index + 1} Full Content:`, messageContent.content);
        }
      });
    } else {
      //console.log('[AIChatContext] No messages in thread');
    }

    //console.log('[AIChatContext] Quick Replies:', savedHistory.quickReplies);
    //console.log('[AIChatContext] ===== END MESSAGE THREAD =====');

    return savedHistory;
  };

  // Helper function to create default chat state
  const defaultChatState = () => ({
    messages: [
      {
        id: 'welcome-message',
        choices: [{
          index: 0,
          message: {
            role: 'assistant',
            content: `I'm here to help with this Q&A question. What would you like to know?`,
            isLoading: false
          }
        }]
      }
    ],
    quickReplies: [
      'Explain why the answer is correct and others are wrong.',
      "Give an example to clarify the answer.",
      "Share references for the correct answer.",
    ]
  });

  // Update chat history for specific Q&A
  const updateChatHistory = (examCode, qnaId, messages, quickReplies) => {
    /* console.log('[AIChatContext] ===== UPDATING CHAT HISTORY =====');
    console.log('[AIChatContext] Update for examCode:', examCode, 'qnaId:', qnaId);
    console.log('[AIChatContext] New messages count:', messages?.length || 0);
    console.log('[AIChatContext] New quickReplies count:', quickReplies?.length || 0); */

    if (!examCode || !qnaId) {
      console.warn('[AIChatContext] updateChatHistory called with invalid params:', { examCode, qnaId });
      return;
    }

    setChatHistories(prev => {
      /* console.log('[AIChatContext] Previous state before update:', {
        totalExams: Object.keys(prev).length,
        examExists: !!prev[examCode],
        qnaExists: !!prev[examCode]?.[qnaId]
      }); */

      // Create a deep copy to ensure we're not mutating the previous state
      const newState = {
        ...prev,
        [examCode]: {
          ...prev[examCode],
          [qnaId]: {
            messages: messages || [],
            quickReplies: quickReplies || []
          }
        }
      };

      /* console.log('[AIChatContext] Updated chat history for exam:', examCode, 'qna:', qnaId, {
        newMessagesCount: newState[examCode][qnaId].messages.length,
        newQuickRepliesCount: newState[examCode][qnaId].quickReplies.length,
        totalExamsAfterUpdate: Object.keys(newState).length,
        totalQnAsForExam: Object.keys(newState[examCode]).length
      }); */

      return newState;
    });
  };

  // Clear chat history for specific Q&A
  const clearChatHistory = (examCode, qnaId) => {
    if (!examCode || !qnaId) {
      console.warn('clearChatHistory called with invalid params:', { examCode, qnaId });
      return;
    }

    setChatHistories(prev => {
      const newState = { ...prev };

      if (newState[examCode]) {
        const examData = { ...newState[examCode] };
        delete examData[qnaId];

        if (Object.keys(examData).length === 0) {
          delete newState[examCode];
        } else {
          newState[examCode] = examData;
        }
      }

      console.log(`Cleared chat history for exam: ${examCode}, qna: ${qnaId}`);
      return newState;
    });
  };

  // Load AI chat data from API response (called during login)
  const loadAIChatFromAPI = (aiChatsData) => {
    console.log('[AIChatContext] ===== LOADING AI CHAT FROM API =====');

    if (!aiChatsData) {
      console.log('[AIChatContext] No AI chat data provided');
      return;
    }

    // Handle different data formats
    let chatData = aiChatsData;

    // If it's an array (from user object), process it
    if (Array.isArray(aiChatsData)) {
      console.log('[AIChatContext] Processing array format AI chat data:', aiChatsData.length, 'chat sessions');

      if (aiChatsData.length === 0) {
        console.log('[AIChatContext] Empty AI chat array, no data to load');
        return;
      }

      // Convert array format to object format
      const formattedChatHistories = {};

      aiChatsData.forEach(chatSession => {
        try {
          const { examCode, qnaId, messages, quickReplies } = chatSession;

          if (examCode && qnaId) {
            if (!formattedChatHistories[examCode]) {
              formattedChatHistories[examCode] = {};
            }

            formattedChatHistories[examCode][qnaId] = {
              messages: messages || [],
              quickReplies: quickReplies || [
                'Explain why the answer is correct and others are wrong.',
                "Give an example to clarify the answer.",
                "Share references for the correct answer.",
              ]
            };

            console.log(`[AIChatContext] Loaded chat for exam: ${examCode}, qna: ${qnaId}`, {
              messagesCount: messages?.length || 0,
              quickRepliesCount: quickReplies?.length || 0
            });
          }
        } catch (error) {
          console.error('[AIChatContext] Error processing chat session:', error, chatSession);
        }
      });

      chatData = formattedChatHistories;
    }
    // If it's already an object (from API getAIChatHistory), use it directly
    else if (typeof aiChatsData === 'object') {
      console.log('[AIChatContext] Processing object format AI chat data');

      // Convert from questionId format to exam/qna format if needed
      const formattedChatHistories = {};

      for (const [questionId, chatSession] of Object.entries(aiChatsData)) {
        try {
          // Check if questionId contains exam/qna info
          if (questionId.includes('_')) {
            const [examCode, qnaId] = questionId.split('_');

            if (examCode && qnaId) {
              if (!formattedChatHistories[examCode]) {
                formattedChatHistories[examCode] = {};
              }

              formattedChatHistories[examCode][qnaId] = {
                messages: chatSession.messages || [],
                quickReplies: chatSession.quickReplies || [
                  'Explain why the answer is correct and others are wrong.',
                  "Give an example to clarify the answer.",
                  "Share references for the correct answer.",
                ]
              };

              console.log(`[AIChatContext] Converted questionId ${questionId} to exam: ${examCode}, qna: ${qnaId}`, {
                messagesCount: chatSession.messages?.length || 0,
                quickRepliesCount: chatSession.quickReplies?.length || 0
              });
            }
          } else {
            // Handle direct exam/qna structure
            const { examCode, qnaId } = chatSession.metadata || {};

            if (examCode && qnaId) {
              if (!formattedChatHistories[examCode]) {
                formattedChatHistories[examCode] = {};
              }

              formattedChatHistories[examCode][qnaId] = {
                messages: chatSession.messages || [],
                quickReplies: chatSession.quickReplies || [
                  'Explain why the answer is correct and others are wrong.',
                  "Give an example to clarify the answer.",
                  "Share references for the correct answer.",
                ]
              };
            }
          }
        } catch (error) {
          console.error('[AIChatContext] Error processing chat session:', error, chatSession);
        }
      }

      chatData = formattedChatHistories;
    }

    console.log('[AIChatContext] Final formatted chat histories:', {
      examsCount: Object.keys(chatData).length,
      examCodes: Object.keys(chatData),
      totalQnAs: Object.values(chatData).reduce((total, exam) => total + Object.keys(exam).length, 0)
    });

    // Update state with loaded data
    setChatHistories(chatData);

    // Save to AsyncStorage
    AsyncStorage.setItem('aiChatHistories', JSON.stringify(chatData))
      .then(() => {
        console.log('[AIChatContext] AI chat data loaded and saved to AsyncStorage');
      })
      .catch(error => {
        console.error('[AIChatContext] Error saving AI chat data to AsyncStorage:', error);
      });

    console.log('[AIChatContext] ===== AI CHAT LOADING COMPLETE =====');
  };

  // Load AI chat data directly from API (for periodic sync)
  const loadAIChatFromAPIDirectly = async () => {
    try {
      console.log('[AIChatContext] ===== LOADING AI CHAT DIRECTLY FROM API =====');

      const apiResponse = await apiClient.getAIChatHistory();
      console.log('[AIChatContext] API response received:', {
        hasData: !!apiResponse,
        dataType: typeof apiResponse,
        keys: apiResponse ? Object.keys(apiResponse) : []
      });

      if (apiResponse) {
        loadAIChatFromAPI(apiResponse);
      } else {
        console.log('[AIChatContext] No AI chat data returned from API');
      }
    } catch (error) {
      console.error('[AIChatContext] Error loading AI chat from API:', error);
    }
  };

  // Clear all chat histories (called during logout)
  const clearAllChatHistories = () => {
    console.log('[AIChatContext] Clearing all chat histories');
    setChatHistories({});
    AsyncStorage.removeItem('aiChatHistories')
      .then(() => {
        console.log('[AIChatContext] All chat histories cleared from AsyncStorage');
      })
      .catch(error => {
        console.error('[AIChatContext] Error clearing chat histories from AsyncStorage:', error);
      });
  };

  return (
    <AIChatContext.Provider value={{
      chatHistories,
      getChatHistory,
      updateChatHistory,
      clearChatHistory,
      loadAIChatFromAPI,
      loadAIChatFromAPIDirectly,
      clearAllChatHistories
    }}>
      {children}
    </AIChatContext.Provider>
  );
};

export const useAIChat = () => useContext(AIChatContext);