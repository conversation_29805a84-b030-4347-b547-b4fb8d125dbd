import React, { createContext, useState, useContext } from 'react';

// Create the context
const FilterContext = createContext();

// Create a provider component
export const FilterProvider = ({ children }) => {
  const [appliedFilters, setAppliedFilters] = useState({
    subjects: [],
    /* dateRange: {
      startDate: null,
      endDate: null
    },
    timeLimit: 30 */
  });

  return (
    <FilterContext.Provider value={{ appliedFilters, setAppliedFilters }}>
      {children}
    </FilterContext.Provider>
  );
};

// Create a custom hook to use the context
export const useFilters = () => {
  const context = useContext(FilterContext);
  if (!context) {
    throw new Error('useFilters must be used within a FilterProvider');
  }
  return context;
};