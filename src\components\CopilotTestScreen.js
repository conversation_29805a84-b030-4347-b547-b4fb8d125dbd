import React, { useRef, useEffect } from 'react';
import { View, ScrollView, StyleSheet, SafeAreaView, Image } from 'react-native';
import {
  CopilotStep,
  useCopilot,
  walkthroughable
} from 'react-native-copilot';
import { Text, Button, useTheme, Appbar } from 'react-native-paper';

// Create walkthroughable components - using walkthroughable HOC
const CopilotView = walkthroughable(View);
const CopilotText = walkthroughable(Text);

const CopilotTestScreen = () => {
  const { colors } = useTheme();
  const { start, copilotEvents, copilot } = useCopilot();

  const scrollViewRef = useRef(null);

  // Manual start function
  const startManualTour = () => {
    // 先滚动到顶部确保所有元素可见
    scrollViewRef.current?.scrollTo({ y: 0, animated: true });
    setTimeout(() => {
      start();
    }, 500);
  };

  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      <Appbar.Header elevated>
        <View style={{ marginLeft: 10, marginRight: 4 }}>
          <Image
            source={require('../../android/app/src/main/res/mipmap-mdpi/ic_launcher_round.png')}
            style={{ width: 28, height: 28 }}
          />
        </View>

        <Appbar.Content
          title={"dummy text"}
          titleStyle={{
            paddingHorizontal: 12,
          }}
        />

        <CopilotStep
          text="This is the app header bar"
          order={5}
          name="headerBar"
          verticalOffset={100}
        >
          <Button
            mode="text"
            labelStyle={{ color: colors.primary, fontWeight: '600', fontSize: 14 }}
            compact
            style={{ marginRight: 8 }}
          >
            Functional key
          </Button>
        </CopilotStep>
      </Appbar.Header>

      <ScrollView
        ref={scrollViewRef}
        style={styles.scrollContent}
        contentContainerStyle={styles.container}
        scrollEventThrottle={16}
      >
        {/* 步骤1 */}
        <CopilotStep
          text="Welcome to the Copilot tutorial!"
          order={1}
          name="header"
        >
          <CopilotView style={styles.header}>
            <Text style={styles.headerText}>Copilot Demo</Text>
          </CopilotView>
        </CopilotStep>

        {/* 步骤2 */}
        <CopilotStep
          text="Click this button to perform an action"
          order={2}
          name="button"
          verticalOffset={100}
        >
          <CopilotView style={styles.buttonContainer}>
            <Button
              mode="contained"
              labelStyle={{ color: '#FFFFFF', fontWeight: '600', fontSize: 16 }}
              onPress={() => console.log('Button clicked')}
              compact
              theme={{ colors: { primary: colors.primary } }}
              style={{ marginRight: 8 }}
            >
              Test button
            </Button>
          </CopilotView>
        </CopilotStep>
        {/* 步骤3 */}
        <CopilotStep
          text="Configure your app settings"
          order={3}
          name="settings"
          verticalOffset={100}
        >
          <CopilotView style={styles.settingsCard}>
            <Text>Notification Settings</Text>
            <Text>Privacy Options</Text>
            <Text>Preferences</Text>
          </CopilotView>
        </CopilotStep>

        {/* 步骤4 */}
        <CopilotStep
          text="This is your profile section"
          order={4}
          name="profile"
          verticalOffset={100}
        >
          <CopilotView style={styles.profileSection}>
            <Text>Profile Picture</Text>
            <Text>Name: Test User</Text>
            <Text>Email: <EMAIL></Text>
          </CopilotView>
        </CopilotStep>

        {/* 手动启动按钮 */}
        <CopilotView style={styles.manualStart}>
          <Button
            mode="contained"
            labelStyle={{ color: '#FFFFFF', fontWeight: '600', fontSize: 16 }}
            onPress={startManualTour}
            compact
            theme={{ colors: { primary: colors.primary } }}
            style={{ marginRight: 8 }}
          >
            Start Manual Tour
          </Button>
        </CopilotView>
      </ScrollView>

    </View>
  );
};

// 样式
const styles = StyleSheet.create({
  scrollContent: {
    flexGrow: 1,
    padding: 16,
    gap: 24,
  },
  container: {
    flex: 1,
  },
  header: {
    padding: 20,
    backgroundColor: '#e3f2fd',
    borderRadius: 10,
    marginBottom: 20,
    zIndex: 1,
    position: 'relative',
  },
  headerText: {
    fontSize: 22,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  buttonContainer: {
    marginVertical: 15,
    zIndex: 1,
    position: 'relative',
  },
  settingsCard: {
    padding: 15,
    backgroundColor: '#f0f4f8',
    borderRadius: 8,
    marginVertical: 10,
    zIndex: 1,
    position: 'relative',
  },
  profileSection: {
    marginTop: 30,
    padding: 15,
    backgroundColor: '#f5f0ff',
    borderRadius: 8,
    zIndex: 1,
    position: 'relative',
  },
  manualStart: {
    marginTop: 40,
    paddingHorizontal: 50,
  },
  headerBar: {
    height: 60,
    backgroundColor: '#6200ee',
    justifyContent: 'center',
    alignItems: 'center',
    paddingTop: 10,
  },
  headerBarText: {
    color: '#fff',
    fontSize: 20,
    fontWeight: 'bold',
  },
  navigationBar: {
    height: 60,
    backgroundColor: '#f8f9fa',
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
  },
  navigationBarText: {
    fontSize: 16,
    color: '#333',
  },
});

export default CopilotTestScreen;