import React from 'react';
import { Al<PERSON>, StyleSheet, Text, View } from 'react-native';
import { List, Divider, useTheme } from 'react-native-paper';
import { useNavigation } from '@react-navigation/native';
import { GoogleSignin } from '@react-native-google-signin/google-signin';
import AsyncStorage from '@react-native-async-storage/async-storage';

import AuthService from '../services/AuthService';
import { useLogin } from '../store/LoginContext';
import { usePurchase } from '../store/PurchaseContext';
import { useExamContext } from '../store/ExamContext';
import { useQnAContext } from '../store/QnAContext';
import { useAICredit } from '../store/AICreditContext';
import DebugPanel from './DebugPanel';

const DebugOptions = () => {
  const { colors } = useTheme();
  const navigation = useNavigation();
  const loginContext = useLogin();
  const { clearCredits } = useAICredit();
  const { 
    selectedExam, 
    isSubscriptionActive, 
    addTestPurchase, 
    logAllPurchases, 
    clearPurchases 
  } = usePurchase();
  const { loadQnA } = useQnAContext();
  const { pendingExamCodeRef } = useQnAContext();

  return (
    <>
      <List.Subheader>Debug Options</List.Subheader>

      <List.Item
        title="Get Google Status"
        description="Check current Google Sign-In status"
        onPress={async () => {
          try {
            const user = await GoogleSignin.getCurrentUser();
            if (user) {
              Alert.alert(
                'Google Sign-In Status',
                JSON.stringify({
                  id: user.user.id,
                  email: user.user.email,
                  name: user.user.givenName + ' ' + user.user.familyName,
                  photo: user.user.photo,
                  scopes: user.scopes,
                  idToken: user.idToken,
                  serverAuthCode: user.serverAuthCode ? 'present' : 'missing'
                }, null, 2)
              );
            } else {
              Alert.alert('Google Sign-In Status', 'No user signed in');
            }
          } catch (error) {
            console.error('Google status error:', error);
            Alert.alert(
              'Google Status Error',
              error.message || 'Failed to get Google sign-in status'
            );
          }
        }}
        left={() => <List.Icon icon="google" />}
        style={styles.listItem}
      />

      <List.Item
        title="Debug Google Login"
        description="Test Google login with detailed error reporting"
        onPress={async () => {
          try {
            Alert.alert('Starting Google Login Debug');

            // Step 1: Check if Google Play Services is available
            try {
              const hasPlayServices = await GoogleSignin.hasPlayServices();
              console.log('Google Play Services available:', hasPlayServices);
            } catch (error) {
              console.error('Google Play Services check failed:', error);
              throw {
                message: `Play Services Error: ${error.message}`,
                code: error.code,
                fullError: JSON.stringify(error, null, 2)
              };
            }

            // Step 2: Attempt sign-in
            Alert.alert('Attempting Google Sign-In');
            const result = await AuthService.loginWithGoogle();

            if (result?.success) {
              Alert.alert('Google Login Success', JSON.stringify({
                userId: result.user?.id,
                email: result.user?.email,
                name: result.user?.name,
                idToken: result.user?.idToken
              }, null, 2));
            } else {
              throw {
                message: result?.message || 'Unknown login error',
                code: result?.code || 'UNKNOWN',
                fullError: JSON.stringify(result, null, 2)
              };
            }
          } catch (error) {
            console.error('Google login debug error:', error);
            Alert.alert(
              'Google Login Debug Error',
              `Error Code: ${error.code || 'UNKNOWN'}\n\n` +
              `Message: ${error.message}\n\n` +
              `Full Error:\n${error.fullError || JSON.stringify(error, null, 2)}`
            );
          }
        }}
        left={() => <List.Icon icon="bug" />}
        style={styles.listItem}
      />

      <List.Item
        title="Clear AI Credits"
        description="Reset AI credits to zero"
        onPress={async () => {
          try {
            await clearCredits();
            Alert.alert('Success', 'AI credits have been reset to zero');
          } catch (error) {
            console.error('Error clearing credits:', error);
            Alert.alert('Error', 'Failed to reset AI credits');
          }
        }}
        left={() => <List.Icon icon="delete" />}
        style={styles.listItem}
      />

      <List.Item
        title="Add Test Purchase for Current Exam"
        description="Adds a test purchase for the currently selected exam"
        onPress={async () => {
          if (selectedExam && addTestPurchase) {
            console.log(`[PURCHASEDBG] Adding test purchase for exam: ${selectedExam.id}`);
            const success = await addTestPurchase(selectedExam.id);
            if (success) {
              console.log(`[PURCHASEDBG] Test purchase added successfully`);
              Alert.alert('Success', 'Test purchase added for the current exam');

              // Refresh purchases to update the UI
              useSubscriptionRefresh();

              // Check premium access
              const hasPremium = await isSubscriptionActive(selectedExam?.exam_code);
              console.log(`[PURCHASEDBG] Premium access after adding test purchase: ${hasPremium}`);

              // Reload QnA with premium content
              if (hasPremium) {
                console.log(`[PURCHASEDBG] Reloading QnA with premium content`);
                await storeSubscriptionStatus(selectedExam.exam_code, true);
              }
            } else {
              console.log(`[PURCHASEDBG] Failed to add test purchase`);
              Alert.alert('Error', 'Failed to add test purchase');
            }
          } else {
            Alert.alert('Error', 'No exam selected or addTestPurchase not available');
          }
        }}
        left={() => <List.Icon icon="bug" />}
        style={styles.listItem}
      />

      <List.Item
        title="Check Premium Access"
        description="Checks premium access for the current exam"
        onPress={async () => {
          if (selectedExam && isSubscriptionActive) {
            const hasPremium = await isSubscriptionActive(selectedExam?.exam_code);
            console.log(`[PURCHASEDBG] Premium access check: ${hasPremium}`);
            Alert.alert('Premium Access', `Premium access for ${selectedExam.exam_code}: ${hasPremium}`);
          } else {
            Alert.alert('Error', 'No exam selected or isSubscriptionActive not available');
          }
        }}
        left={() => <List.Icon icon="check-circle" />}
        style={styles.listItem}
      />

      <List.Item
        title="Reload QnA with Premium Content"
        description="Forces QnA reload with is_free=false"
        onPress={async () => {
          if (selectedExam && loadQnA) {
            console.log(`[PURCHASEDBG] Forcing QnA reload with premium content`);
            await storeSubscriptionStatus(selectedExam.exam_code, true);
            Alert.alert('Success', 'QnA reloaded with premium content');
          } else {
            Alert.alert('Error', 'No exam selected or loadQnA not available');
          }
        }}
        left={() => <List.Icon icon="refresh" />}
        style={styles.listItem}
      />

      <List.Item
        title="Load Premium Content Directly"
        description="Loads premium content if user has premium access"
        onPress={async () => {
          if (selectedExam && loadQnA) {
            // Check premium access first
            const hasPremiumAccess = await isSubscriptionActive(selectedExam?.exam_code);
            console.log(`[PURCHASEDBG] Premium access check: ${hasPremiumAccess}`);

            if (hasPremiumAccess) {
              console.log(`[PURCHASEDBG] Loading premium content directly`);
              await storeSubscriptionStatus(selectedExam.exam_code, true);
              Alert.alert('Success', 'Premium content loaded directly');
            } else {
              console.log(`[PURCHASEDBG] No premium access, cannot load premium content`);
              Alert.alert('No Premium Access', 'You do not have premium access to this exam. Please purchase premium access first.');
            }
          } else {
            Alert.alert('Error', 'No exam selected or loadPremiumContent not available');
          }
        }}
        left={() => <List.Icon icon="crown" />}
        style={styles.listItem}
      />

      <List.Item
        title="Log All Purchases"
        description="Logs all purchases to the console"
        onPress={() => {
          if (logAllPurchases) {
            logAllPurchases();
            Alert.alert('Success', 'All purchases logged to console');
          } else {
            Alert.alert('Error', 'logAllPurchases not available');
          }
        }}
        left={() => <List.Icon icon="format-list-bulleted" />}
        style={styles.listItem}
      />

      <List.Item
        title="Test AdMob"
        description="Test AdMob integration"
        onPress={() => navigation.navigate('AdmobTestScreen')}
        left={() => <List.Icon icon="advertisements" />}
        style={styles.listItem}
      />

      <List.Item
        title="Test Copilot"
        description="Test Copilot integration"
        onPress={() => navigation.navigate('CopilotTestScreen')}
        left={() => <List.Icon icon="rocket-launch" />}
        style={styles.listItem}
      />

      <Divider />
      <DebugPanel />
    </>
  );
};

const styles = StyleSheet.create({
  listItem: {
    paddingVertical: 12,
    paddingHorizontal: 16,
  },
});

export default DebugOptions;