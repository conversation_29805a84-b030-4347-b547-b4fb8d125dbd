import {MD3DarkTheme, MD3LightTheme, MD3Theme} from 'react-native-paper';

export type ExtendedTheme = MD3Theme & {
  colors: MD3Theme['colors'] & {
    gray: string;
    assistanceText:string;
    border:string;
    borderPrimary:string;
    white:string;
  };
};

const lightTheme: ExtendedTheme = {
  ...MD3LightTheme,
  colors: {
    ...MD3LightTheme.colors,
    primary: '#6200ee',
    background: '#ffffff',
    gray: '#f6f6f6',
    assistanceText:'#121212',
    border:'#363F3F',
    borderPrimary:'#ccc',
    white:'#ffffff',
  },
};

const darkTheme: ExtendedTheme = {
  ...MD3DarkTheme,
  colors: {
    ...MD3DarkTheme.colors,
    primary: '#bb86fc',
    background: '#121212',
    gray: '#232323',
    assistanceText:'#ffffff',
    border:'#ffffff',
    borderPrimary:'#ccc',
    white:'#ffffff'
  },
};
const fonts = {
  bold: 'Roboto-Bold',
  regular: 'Roboto-Regular',
  medium: 'Roboto-Medium',
  semiBold: 'Roboto-SemiBold',
};

export {lightTheme, darkTheme, fonts};
