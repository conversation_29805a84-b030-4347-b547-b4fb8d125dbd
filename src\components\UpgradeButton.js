import React, { useState } from 'react';
import { StyleSheet, View } from 'react-native';
import { Card, Text, Button, useTheme } from 'react-native-paper';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';

const UpgradeButton = ({ onPress }) => {
    const { colors } = useTheme();
    return (
        <>
            <Card style={[styles.upgradeCard, { backgroundColor: colors.primaryContainer }]}>
                <Card.Content style={styles.upgradeContent}>
                    <MaterialCommunityIcons
                        name="crown-outline"
                        size={28}
                        color={colors.onPrimaryContainer}
                    />
                    <View style={styles.upgradeText}>
                        <Text variant="bodyLarge" style={{ color: colors.onPrimaryContainer }}>
                            Unlock Full Access
                        </Text>
                        <Text variant="bodySmall" style={{ color: colors.onPrimaryContainer }}>
                            Get all questions and premium features
                        </Text>
                    </View>
                    <Button
                        mode="contained"
                        onPress={onPress}
                        style={styles.upgradeButton}
                        labelStyle={{ color: '#FFFFFF', fontWeight: '600', fontSize: 16 }}
                        theme={{ colors: { primary: colors.primary } }}
                    >
                        Upgrade Now
                    </Button>
                </Card.Content>
            </Card>
        </>
    );
};

const styles = StyleSheet.create({
    upgradeCard: {
        marginTop: 4,
        marginBottom: 20,
    },
    upgradeContent: {
        flexDirection: 'row',
        alignItems: 'center',
        paddingVertical: 20,
        paddingHorizontal: 16,
    },
    upgradeText: {
        flex: 1,
        marginLeft: 16,
    },
    upgradeButton: {
        marginTop: 8,
        borderRadius: 8,
    },
});

export default UpgradeButton;