import AsyncStorage from '@react-native-async-storage/async-storage';
import apiClient from './ApiClient';

/**
 * Service for handling user progress sync coordination
 * Works with RealTimeSyncService for bulk progress updates
 * NOTE: Individual question-level sync is disabled in favor of bulk updates
 */
class UserProgressSyncService {
    constructor() {
        // Enable/disable sync - start disabled until user logs in
        this.syncEnabled = false;

        // Track current user ID
        this.currentUserId = null;

        console.log('[UserProgressSyncService] Initialized (bulk sync mode - individual sync disabled)');
    }

    /**
     * Enable or disable sync operations
     * @param {boolean} enabled - Whether to enable sync
     */
    setSyncEnabled(enabled) {
        this.syncEnabled = enabled;
        console.log(`[UserProgressSyncService] Sync ${enabled ? 'enabled' : 'disabled'}`);
    }

    /**
     * Initialize sync service for a logged-in user
     * @param {string} userId - User ID
     */
    initializeForUser(userId) {
        if (!userId) {
            console.warn('[UserProgressSyncService] Cannot initialize without userId');
            return;
        }

        this.currentUserId = userId;
        this.syncEnabled = true;
        console.log(`[UserProgressSyncService] Initialized for user: ${userId}`);
    }

    /**
     * Handle user logout - disable sync and clear state
     */
    handleUserLogout() {
        console.log('[UserProgressSyncService] User logged out, disabling sync');
        this.syncEnabled = false;
        this.currentUserId = null;
        this.clearState();
    }

    /**
     * Check if sync should be performed (user logged in and sync enabled)
     * @returns {boolean} - True if sync should be performed
     */
    shouldSync() {
        const shouldSync = this.syncEnabled && this.currentUserId;
        if (!shouldSync) {
            console.log(`[UserProgressSyncService] Sync check failed: enabled=${this.syncEnabled}, userId=${!!this.currentUserId}`);
        }
        return shouldSync;
    }

    /**
     * Generate a unique key for tracking progress state
     * @param {string} examId - Exam ID
     * @param {string} subject - Subject ID
     * @param {string} questionId - Question ID
     * @param {string} action - Action type
     * @returns {string} - Unique key
     */
    generateProgressKey(examId, subject, questionId, action) {
        return `${examId}:${subject}:${questionId}:${action}`;
    }

    /**
     * Check if a progress update represents a change from the last synced state
     * @param {string} examId - Exam ID
     * @param {string} subject - Subject ID
     * @param {string} questionId - Question ID
     * @param {string} action - Action type
     * @param {Object} options - Action options
     * @param {Object} [currentProgressContext] - Current UserProgressContext state for comparison
     * @returns {boolean} - True if this represents a change
     */
    hasProgressChanged(examId, subject, questionId, action, options = {}, currentProgressContext = null) {
        const key = this.generateProgressKey(examId, subject, questionId, action);
        const lastState = this.lastSyncedState.get(key);

        // If we have current progress context, use it for more accurate change detection
        if (currentProgressContext) {
            const examProgress = currentProgressContext[examId];
            const subjectProgress = examProgress?.[subject];

            if (!subjectProgress) {
                // No progress data for this subject, this is a new change
                return true;
            }

            // Check based on action type
            if (action === 'browsed') {
                // Check if this question was already browsed
                const wasBrowsed = subjectProgress.browsed?.some(item => item.id === questionId);
                if (!wasBrowsed) {
                    // Question not browsed before, this is a change
                    return true;
                }

                // If already browsed, check if we've synced this browsed state before
                if (!lastState) {
                    // Not synced before, this is a change
                    return true;
                }

                // Already browsed and synced, no change needed
                return false;
            }

            if (action === 'bookmarked') {
                // Check current bookmark state in context
                const isCurrentlyBookmarked = subjectProgress.bookmarked?.some(item => item.id === questionId);

                if (!lastState) {
                    // No previous sync state, sync current state
                    return true;
                }

                // Compare with last synced state
                return lastState.isBookmarked !== isCurrentlyBookmarked;
            }

            if (action === 'answered') {
                // Check current answer state in context
                const isCurrentlyCorrect = subjectProgress.correct?.some(item => item.id === questionId);
                const isCurrentlyIncorrect = subjectProgress.incorrect?.some(item => item.id === questionId);

                // Determine current correct state
                let currentIsCorrect = null;
                if (isCurrentlyCorrect) {
                    currentIsCorrect = true;
                } else if (isCurrentlyIncorrect) {
                    currentIsCorrect = false;
                }

                if (!lastState) {
                    // No previous sync state, sync if there's an answer
                    return currentIsCorrect !== null;
                }

                // Compare with last synced state
                return lastState.isCorrect !== currentIsCorrect;
            }
        }

        // Fallback to original logic if no context provided
        if (!lastState) {
            // No previous state, this is a new change
            return true;
        }

        // Compare current state with last synced state
        const currentState = {
            action,
            isBookmarked: options.isBookmarked,
            isCorrect: options.isCorrect,
            timestamp: Date.now()
        };

        // For bookmarked action, check if bookmark state changed
        if (action === 'bookmarked') {
            return lastState.isBookmarked !== currentState.isBookmarked;
        }

        // For answered action, check if correct state changed
        if (action === 'answered') {
            return lastState.isCorrect !== currentState.isCorrect;
        }

        // For browsed action, check if we've synced this before
        if (action === 'browsed') {
            // Only sync if we haven't synced this browsed state before
            return !lastState;
        }

        return false;
    }

    /**
     * Update the last synced state for a progress item
     * @param {string} examId - Exam ID
     * @param {string} subject - Subject ID
     * @param {string} questionId - Question ID
     * @param {string} action - Action type
     * @param {Object} options - Action options
     */
    updateLastSyncedState(examId, subject, questionId, action, options = {}) {
        const key = this.generateProgressKey(examId, subject, questionId, action);
        const state = {
            action,
            isBookmarked: options.isBookmarked,
            isCorrect: options.isCorrect,
            timestamp: Date.now()
        };

        this.lastSyncedState.set(key, state);
    }

    /**
     * Sync a single progress update (now handled by bulk sync)
     * @param {string} examId - Exam ID
     * @param {string} subject - Subject ID
     * @param {string} questionId - Question ID
     * @param {string} action - Action type
     * @param {Object} options - Action options
     * @param {Object} [currentProgressContext] - Current UserProgressContext state for change detection
     * @returns {Promise<Object>} - API response
     */
    async syncProgressUpdate(examId, subject, questionId, action, options = {}, currentProgressContext = null) {
        if (!this.shouldSync()) {
            console.log('[UserProgressSyncService] Sync not available (user not logged in or sync disabled), skipping update');
            return { success: false, reason: 'sync_not_available' };
        }

        console.log(`[UserProgressSyncService] Progress update logged: ${examId}/${subject}/${questionId} - ${action} (will be synced via bulk update)`, {
            action,
            isBookmarked: options.isBookmarked,
            isCorrect: options.isCorrect
        });

        // Individual sync is disabled - progress will be synced via RealTimeSyncService bulk update
        return {
            success: true,
            reason: 'bulk_sync_mode',
            message: 'Progress logged locally, will be synced via bulk update'
        };
    }

    /**
     * Perform the actual API call for progress sync
     * @param {string} examId - Exam ID
     * @param {string} subject - Subject ID
     * @param {string} questionId - Question ID
     * @param {string} action - Action type
     * @param {Object} options - Action options
     * @param {Object} [currentProgressContext] - Current UserProgressContext state for determining synced state
     * @returns {Promise<Object>} - API response
     */
    async performProgressSync(examId, subject, questionId, action, options = {}, currentProgressContext = null) {
        try {
            const response = await apiClient.updateUserProgress(examId, subject, questionId, action, options);

            // For successful sync, determine the actual state that was synced
            let syncedOptions = { ...options };

            // For answered actions, if options.isCorrect is not provided,
            // we need to determine it from the current context
            if (action === 'answered' && typeof options.isCorrect !== 'boolean' && currentProgressContext) {
                const examProgress = currentProgressContext[examId];
                const subjectProgress = examProgress?.[subject];

                if (subjectProgress) {
                    const isCurrentlyCorrect = subjectProgress.correct?.some(item => item.id === questionId);
                    const isCurrentlyIncorrect = subjectProgress.incorrect?.some(item => item.id === questionId);

                    if (isCurrentlyCorrect) {
                        syncedOptions.isCorrect = true;
                    } else if (isCurrentlyIncorrect) {
                        syncedOptions.isCorrect = false;
                    }
                }
            }

            // For bookmarked actions, if options.isBookmarked is not provided,
            // determine it from the current context
            if (action === 'bookmarked' && typeof options.isBookmarked !== 'boolean' && currentProgressContext) {
                const examProgress = currentProgressContext[examId];
                const subjectProgress = examProgress?.[subject];

                if (subjectProgress) {
                    const isCurrentlyBookmarked = subjectProgress.bookmarked?.some(item => item.id === questionId);
                    syncedOptions.isBookmarked = isCurrentlyBookmarked;
                }
            }

            // Update the last synced state on successful sync
            this.updateLastSyncedState(examId, subject, questionId, action, syncedOptions);

            console.log(`[UserProgressSyncService] ✅ Progress synced successfully: ${examId}/${subject}/${questionId}:${action}`, {
                syncedOptions
            });

            return {
                success: true,
                data: response,
                examId,
                subject,
                questionId,
                action,
                options: syncedOptions
            };
        } catch (error) {
            console.error(`[UserProgressSyncService] ❌ Progress sync failed: ${examId}/${subject}/${questionId}:${action}`, error);

            return {
                success: false,
                error: error.message,
                examId,
                subject,
                questionId,
                action,
                options
            };
        }
    }

    /**
     * Sync progress update with debouncing (now just logs - handled by bulk sync)
     * @param {string} examId - Exam ID
     * @param {string} subject - Subject ID
     * @param {string} questionId - Question ID
     * @param {string} action - Action type
     * @param {Object} options - Action options
     * @param {Object} [currentProgressContext] - Current UserProgressContext state for change detection
     * @returns {Promise<Object>} - API response
     */
    async syncProgressUpdateDebounced(examId, subject, questionId, action, options = {}, currentProgressContext = null) {
        // Just call the regular sync method (which now just logs)
        return this.syncProgressUpdate(examId, subject, questionId, action, options, currentProgressContext);
    }

    /**
     * Clear all cached state (useful for logout or user switching)
     */
    clearState() {
        console.log('[UserProgressSyncService] State cleared (bulk sync mode)');
    }

    /**
     * Get sync status information
     * @returns {Object} - Current sync status
     */
    getSyncStatus() {
        return {
            syncEnabled: this.syncEnabled,
            currentUserId: this.currentUserId,
            isLoggedIn: !!this.currentUserId,
            shouldSync: this.shouldSync(),
            syncMode: 'bulk',
            message: 'Individual sync disabled - using bulk sync via RealTimeSyncService'
        };
    }
}

// Create and export a singleton instance
const userProgressSyncService = new UserProgressSyncService();
export default userProgressSyncService;
