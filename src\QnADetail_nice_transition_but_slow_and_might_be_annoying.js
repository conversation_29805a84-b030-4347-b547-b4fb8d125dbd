import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  ScrollView,
  TouchableOpacity,
  StyleSheet,
  Image,
  Modal,
  Dimensions,
  Animated
} from 'react-native';
import FastImage from 'react-native-fast-image';
import { PinchGestureHandler, State } from 'react-native-gesture-handler';
import {
  Text,
  IconButton,
  useTheme,
  Appbar,
} from 'react-native-paper';
import { useNavigation, useRoute } from '@react-navigation/native';
import AntDesign from 'react-native-vector-icons/AntDesign';
import Entypo from 'react-native-vector-icons/Entypo';
import Ionicons from 'react-native-vector-icons/Ionicons';
import { useUserProgress } from './store/UserProgressContext';
import { usePreferencesContext } from './store/PreferencesContext';
import { shuffleAndReorderChoices } from './utils/shuffleAndReorderChoices';
import { useExamContext } from './store/ExamContext';
import apiClient from './services/ApiClient';
import { red100 } from 'react-native-paper/lib/typescript/styles/themes/v2/colors';
import ScaledImage from './components/ScaledImage';

const QnaDetail = () => {
  const [dimensions, setDimensions] = useState({
    width: Dimensions.get('window').width,
    height: Dimensions.get('window').height
  });

  useEffect(() => {
    const subscription = Dimensions.addEventListener('change', handleDimensionsChange);
    return () => subscription?.remove();
  }, []);

  const handleDimensionsChange = ({ window }) => {
    setDimensions({
      width: window.width,
      height: window.height
    });
  };

  const scrollViewRef = useRef(null);
  const { colors } = useTheme();
  const navigation = useNavigation();
  const route = useRoute();
  const { selectedExam } = useExamContext();
  const {
    initialIndex = 0,
    activeTabLabel = 'All',
    userAnswers = {},
    fromQuizResult = false
  } = route.params;

  const filteredData = route.params.filteredData || [];
  const [currentIndex, setCurrentIndex] = useState(initialIndex);
  const currentItem = filteredData[currentIndex] || null;

  const [shuffledChoices, setShuffledChoices] = useState([]);

  const [showAnswer, setShowAnswer] = useState(false);
  const [manualShowAnswer, setManualShowAnswer] = useState(false);
  const [selectedChoice, setSelectedChoice] = useState([]);

  const [mappedAnswer, setMappedAnswer] = useState([]);
  const [newKeyToOriginalKeyMap, setNewKeyToOriginalKeyMap] = useState({});

  const { shouldShuffleChoices } = usePreferencesContext();

  const { progress, updateProgress, toggleBookmark } = useUserProgress();
  const isBookmarked = Boolean(
    selectedExam?.id &&
    currentItem?.subject &&
    currentItem?.id &&
    progress[selectedExam.id]?.[currentItem.subject]?.bookmarked?.some(b => b.id === currentItem.id)
  );

  const [isImageModalVisible, setIsImageModalVisible] = useState(false);
  const [selectedImageUri, setSelectedImageUri] = useState(null);
  const [scale, setScale] = useState(1);
  const [slideDirection, setSlideDirection] = useState(null);
  const slideAnim = useRef(new Animated.Value(0)).current;
  const opacityAnim = useRef(new Animated.Value(1)).current;

  const handleImagePress = (uri) => {
    setSelectedImageUri(uri);
    setIsImageModalVisible(true);
    setScale(1); // Reset scale when opening
  };

  const handlePinchGesture = (event) => {
    if (event.nativeEvent.state === State.ACTIVE) {
      setScale(event.nativeEvent.scale);
    }
  };
  const renderHtmlContent = (htmlContent, isChoice) => {
    const parts = htmlContent.split(/(<img[^>]+>)/g);

    return parts.map((part, index) => {
      if (part.startsWith('<img')) {
        const srcMatch = part.match(/src="([^"]+)"/);
        if (!srcMatch) return null;
        let src = srcMatch[1]
          .split('/')
          .map(segment => encodeURIComponent(segment))
          .join('%2F');

        src = apiClient.baseUrl + '/image/' + src;

        return (
          <TouchableOpacity
            key={`img-${index}`}
            onPress={() => handleImagePress(src)}
            style={{ flexDirection: 'row' }}
          >
            <ScaledImage
              uri={src}
              width={isChoice ? (dimensions.width - 85) : dimensions.width - 35}
            />
          </TouchableOpacity>
        );
      } else {
        const isAfterImage = index > 0 && parts[index - 1].startsWith('<img');
        const isBeforeImage = index < parts.length - 1 && parts[index + 1].startsWith('<img');
        let processedPart = part;

        if (isAfterImage) {
          processedPart = processedPart
            .replace(/^(\s*<br\s*\/?>\s*)+/gi, '')
            .replace(/^\n+/g, '');
        }

        if (isBeforeImage) {
          processedPart = processedPart
            .replace(/(\s*<br\s*\/?>\s*)+$/gi, '')
            .replace(/\n+$/g, '');
        }

        if (processedPart.trim() === '') return null;

        const textParts = processedPart.split(/<br\s*\/?>/gi);

        return textParts.map((textSegment, textIndex) => (
          <Text
            key={`text-${index}-${textIndex}`}
            style={{
              color: colors.onSurface,
              fontSize: 16,
              lineHeight: 24
            }}
          >
            {textSegment.replace(/<\/?[^>]+(>|$)/g, '')}
          </Text>
        ));
      }
    });
  };

  useEffect(() => {
    if (showAnswer && scrollViewRef.current) {
      setTimeout(() => {
        scrollViewRef.current?.scrollToEnd({ animated: true });
      }, 100);
    }
  }, [showAnswer]);

  useEffect(() => {
    if (currentItem?.choices) {
      if (fromQuizResult || !shouldShuffleChoices) {
        // Maintain original order when shuffle is disabled or in quiz review
        setShuffledChoices(currentItem.choices);
        setMappedAnswer(currentItem.answer);
        const map = {};
        currentItem.choices.forEach(choice => {
          const key = Object.keys(choice)[0];
          map[key] = key;
        });
        setNewKeyToOriginalKeyMap(map);
      } else {

        const { shuffledChoices, mappedAnswer, newKeyToOriginalKeyMap } =
          shuffleAndReorderChoices(currentItem.choices, currentItem.answer);
        setShuffledChoices(shuffledChoices);
        setMappedAnswer(mappedAnswer);
        setNewKeyToOriginalKeyMap(newKeyToOriginalKeyMap);
      }
    }
  }, [currentItem, filteredData, navigation]);

  const handleBookmarkToggle = () => {
    if (!selectedExam?.id || !currentItem?.subject || !currentItem?.id) return;
    toggleBookmark(selectedExam.id, currentItem.subject, currentItem.id);
  };

  useEffect(() => {
    if (selectedExam?.id && currentItem?.subject && currentItem?.id) {
      updateProgress(selectedExam.id, currentItem.subject, currentItem.id);
    }
  }, [currentItem, selectedExam, updateProgress]);

  useEffect(() => {
    if (currentItem?.choices) {
    } else {
      // If no valid item, go back to prevent blank screen
      if (filteredData.length === 0) {
        navigation.goBack();
      }
    }
  }, [currentItem, filteredData, navigation]);

  const safeSetCurrentIndex = (newIndex, direction) => {
    if (newIndex >= 0 && newIndex < filteredData.length) {
      // Set the slide direction
      setSlideDirection(direction);

      // Start the slide out animation
      slideAnim.setValue(0);

      // Fade out current content - use a very short duration for quick transitions
      Animated.timing(opacityAnim, {
        toValue: 0,
        duration: 50, // Very short fade out
        useNativeDriver: true,
      }).start(() => {
        // Update the index and reset state
        setShuffledChoices([]); // Clear previous choices immediately
        setCurrentIndex(newIndex);
        resetState();

        // Prepare the slide in animation
        slideAnim.setValue(direction === 'left' ? 50 : -50); // Reduced slide distance

        // Fade in and slide in the new content - use short durations
        Animated.parallel([
          Animated.timing(opacityAnim, {
            toValue: 1,
            duration: 50, // Short fade in
            useNativeDriver: true,
          }),
          Animated.timing(slideAnim, {
            toValue: 0,
            duration: 50, // Short slide
            useNativeDriver: true,
          }),
        ]).start();
      });
    }
  };

  const handlePrev = () => safeSetCurrentIndex(currentIndex - 1, 'right');
  const handleNext = () => safeSetCurrentIndex(currentIndex + 1, 'left');

  const resetState = () => {
    setSelectedChoice([]);
    setShowAnswer(false);
    setManualShowAnswer(false);
  };

  useEffect(() => {
    let userAnswer = [];
    let forceShowAnswer = false;

    if (fromQuizResult) {
      userAnswer = (userAnswers[currentItem?.originalIndex] || []).filter(Boolean);
      forceShowAnswer = true;
    } else {
      userAnswer = Array.isArray(currentItem?.userSelectedAnswer)
        ? currentItem.userSelectedAnswer
        : [];
    }

    if (JSON.stringify(selectedChoice) !== JSON.stringify(userAnswer)) {
      setSelectedChoice(userAnswer);
    }
  }, [currentItem, fromQuizResult]); // Removed manualShowAnswer from dependencies

  useEffect(() => {
    if (!manualShowAnswer) {
      const isMC2 = currentItem?.type === 'mc2';
      const shouldShowAnswer = fromQuizResult || (selectedChoice.length > 0 && !isMC2);
      setShowAnswer(shouldShowAnswer);
    }
  }, [currentItem, fromQuizResult, manualShowAnswer, selectedChoice]);

  const toggleChoice = (key) => {
    if (!showAnswer && !fromQuizResult) {
      const originalKey = newKeyToOriginalKeyMap[key];
      setSelectedChoice(prev =>
        prev.includes(originalKey)
          ? prev.filter(k => k !== originalKey)
          : [...prev, originalKey]
      );
    } else if (!showAnswer) {
      setSelectedChoice(prev =>
        prev.includes(key)
          ? prev.filter(k => k !== key)
          : [...prev, key]
      );
    }
  };

  const handleShowAnswerToggle = () => {
    setManualShowAnswer(true);
    setShowAnswer(prev => !prev);
  };

  return (
    <View style={{ flex: 1, backgroundColor: colors.background }}>
      <Appbar.Header elevated>
        <Appbar.BackAction onPress={() => navigation.goBack()} />
        <Appbar.Content title={activeTabLabel} />
        <View style={{
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'space-between',
          paddingHorizontal: 12,
          paddingVertical: 10,
        }}>
          <View style={{ flexDirection: 'row', alignItems: 'center', gap: 16 }}>

            <Text style={{ fontSize: 14, fontWeight: '600', color: colors.onSurface }}>
              {currentIndex + 1} / {filteredData.length}
            </Text>
          </View>
        </View>
      </Appbar.Header>

      <Animated.ScrollView
        ref={scrollViewRef}
        contentContainerStyle={{ padding: 16 }}
        style={{
          transform: [{ translateX: slideAnim }],
          opacity: opacityAnim
        }}
      >

        {shuffledChoices.length ? (
          <>
            <Text variant="labelSmall" style={{ color: colors.onSurfaceVariant }}>
              {currentItem?.subjectName}
            </Text>

            <View>
              {renderHtmlContent(currentItem?.question || '', false)}
            </View>

            {showAnswer && currentItem?.explanation && currentItem?.explanation?.trim() !== '' ? (
              <View style={{
                marginTop: 10,
                padding: 16,
                backgroundColor: colors.surface,
                borderRadius: 8,
                borderLeftWidth: 4,
                borderLeftColor: colors.primary,
                borderTopLeftRadius: 4,
                borderBottomLeftRadius: 4,
              }}>
                <View style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  gap: 8,
                  marginBottom: 12
                }}>
                  <Ionicons name="information-circle" size={20} color={colors.primary} />
                  <Text variant="titleSmall" style={{
                    color: colors.primary,
                    fontWeight: '700'
                  }}>
                    Explanation
                  </Text>
                </View>
                <Text style={{
                  fontSize: 14,
                  lineHeight: 20,
                  color: colors.onSurfaceVariant,  // Changed from colors.onSurface
                }}>
                  {currentItem?.ai_explanation}
                </Text>
              </View>
            ) : null}

            <Text
              style={[
                styles.questionType,
                {
                  color: colors.onSurfaceVariant,
                  paddingTop: 10,
                  paddingBottom: 5
                }
              ]}>
              {(currentItem?.answer?.length || 0) > 1
                ? "Select all that apply"
                : "Choose the correct answer"}
            </Text>

            <View style={{ gap: 10 }} key={currentIndex}>
              {shuffledChoices.map((choiceObj) => {
                const [key, value] = Object.entries(choiceObj)[0];
                const isCorrect = mappedAnswer.includes(key);
                const isSelected = selectedChoice.includes(newKeyToOriginalKeyMap[key]);
                const showIncorrect = showAnswer && isSelected && !isCorrect;
                const showCorrect = showAnswer && isCorrect;

                return (
                  <TouchableOpacity
                    key={key}
                    onPress={() => toggleChoice(key)}
                    style={{
                      borderWidth: 2,
                      borderColor: showCorrect ? '#4CAF50' :
                        showIncorrect ? '#f44336' :
                          isSelected ? colors.primary : colors.surfaceVariant,
                      borderRadius: 10,
                      padding: 14,
                      backgroundColor: isSelected ? `${colors.primary}20` : colors.surface,
                      flexDirection: 'row',
                      justifyContent: 'space-between',
                      alignItems: 'center',
                    }}
                  >
                    <View style={{ flexDirection: 'row', alignItems: 'flex-start' }}>
                      <Text style={{
                        fontSize: 16,
                        fontWeight: '500',
                        marginRight: 8,
                        lineHeight: 24, // Match text line height
                        color: colors.onSurface
                      }}>
                        {key}.
                      </Text>

                      <View style={{ flex: 1 }}>
                        {renderHtmlContent(value, true)}
                      </View>
                    </View>
                    {showCorrect && <AntDesign name="check" size={18} color={'#4CAF50'} />}
                    {showIncorrect && <Entypo name="cross" size={18} color={'#f44336'} />}
                  </TouchableOpacity>
                );
              })}
            </View>
          </>
        ) : (
          null
        )}
      </Animated.ScrollView>

      {/* Image Modal */}
      <Modal
        visible={isImageModalVisible}
        transparent={true}
        onRequestClose={() => setIsImageModalVisible(false)}
      >
        <View style={styles.modalContainer}>
          <PinchGestureHandler onGestureEvent={handlePinchGesture}>
            <FastImage
              style={[
                styles.fullImage,
                {
                  width: dimensions.width,
                  height: dimensions.height / 2,
                  transform: [{ scale }]
                },
              ]}
              source={{ uri: selectedImageUri }}
              resizeMode={FastImage.resizeMode.contain}
            />
          </PinchGestureHandler>
          <TouchableOpacity
            style={styles.closeButton}
            onPress={() => setIsImageModalVisible(false)}
          >
            <Text style={{ color: 'white', fontSize: 18 }}>Close</Text>
          </TouchableOpacity>
        </View>
      </Modal>

      {/* Original Bottom Bar Styling */}
      <View style={{
        flexDirection: 'row',
        justifyContent: 'space-between',
        paddingHorizontal: 12,
        paddingVertical: 8,
        borderTopWidth: 1,
        borderColor: colors.surfaceVariant,
        backgroundColor: colors.background,
      }}>
        <IconButton
          icon="chevron-left"
          disabled={currentIndex === 0}
          onPress={handlePrev}
        />


        <IconButton
          icon={isBookmarked ? 'bookmark' : 'bookmark-outline'}
          onPress={handleBookmarkToggle}
        />

        <IconButton
          icon={() => (
            <Image source={require('../assets/ai.png')} style={{
              height: 30,
              width: 30,
              tintColor: colors.onSurface
            }} />
          )}
          onPress={() => navigation.navigate('ChatScreen', {
            examCode: selectedExam?.id,
            qnaId: currentItem?._id
          })}
        />

        {!fromQuizResult && (
          <IconButton
            icon={() => (
              <Image source={require('../assets/answer.png')} style={{
                height: 22,
                width: 22,
                tintColor: showAnswer ? '#4CAF50' : colors.onSurface
              }} />
            )}
            onPress={handleShowAnswerToggle}
          />
        )}

        <View style={{ flexDirection: 'row', alignItems: 'center', gap: 16 }}>
          <IconButton
            icon="chevron-right"
            disabled={currentIndex === filteredData.length - 1}
            onPress={handleNext}
          />
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  center: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerRightContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 16,
    paddingHorizontal: 12,
  },
  questionText: {
    fontSize: 16,
    fontWeight: '600',
    marginTop: 10,
    marginBottom: 20,
  },
  questionType: {
    fontSize: 14,
    fontWeight: '500',
    marginVertical: 8,
  },
  explanationHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    marginBottom: 12
  },
  bottomBar: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderTopWidth: 1,
  },
  choiceItem: {
    borderWidth: 2,
    borderRadius: 10,
    padding: 14,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  loadingOverlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'rgba(255,255,255,0.7)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1,
  },
  modalContainer: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.9)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  fullImage: {
    width: Dimensions.get('window').width,
    height: Dimensions.get('window').height / 2,
  },
  closeButton: {
    position: 'absolute',
    top: 40,
    right: 20,
    padding: 10,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    borderRadius: 5,
  },
});

export default QnaDetail;
