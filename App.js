import React, { useEffect, useState, useCallback } from 'react';
import { View, StyleSheet, Appearance, AppState } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { CommonActions, NavigationContainer } from '@react-navigation/native';
import { Text, BottomNavigation, Provider as PaperProvider, adaptNavigationTheme } from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import BootSplash from 'react-native-bootsplash';
import { DarkTheme as NavigationDarkTheme, DefaultTheme as NavigationDefaultTheme } from '@react-navigation/native';
import { GoogleSignin } from '@react-native-google-signin/google-signin';
import { CopilotProvider } from 'react-native-copilot';
import CopilotTestScreen from './src/components/CopilotTestScreen';

import AuthService from './src/services/AuthService';
import apiClient from './src/services/ApiClient';
import RealTimeSyncService from './src/services/RealTimeSyncService';
import { PurchaseProvider } from './src/store/PurchaseContext';
import { AICreditProvider } from './src/store/AICreditContext';
import BuyNowModal from './src/components/BuyNowModal';


const { LightTheme, DarkTheme } = adaptNavigationTheme({
  reactNavigationLight: NavigationDefaultTheme,
  reactNavigationDark: NavigationDarkTheme,
});

const Tab = createBottomTabNavigator();

const HomeScreen = () => (
  <View style={styles.container}>
    <Text variant="headlineMedium">Home!</Text>
  </View>
);

const SettingsScreen = () => (
  <View style={styles.container}>
    <Text variant="headlineMedium">Settings!</Text>
  </View>
);

const CustomTabBar = ({ navigation, state, descriptors, insets }) => {
  const handleTabPress = useCallback(
    ({ route, preventDefault }) => {
      const event = navigation.emit({
        type: 'tabPress',
        target: route.key,
        canPreventDefault: true,
      });

      if (event.defaultPrevented) {
        preventDefault();
      } else {
        navigation.dispatch({
          ...CommonActions.navigate(route.name, route.params),
          target: state.key,
        });
      }
    },
    [navigation, state],
  );

  return (
    <BottomNavigation.Bar
      navigationState={state}
      safeAreaInsets={insets}
      onTabPress={handleTabPress}
      renderIcon={({ route, focused, color }) =>
        descriptors[route.key].options.tabBarIcon?.({
          focused,
          color,
          size: 24,
        }) || null
      }
      getLabelText={({ route }) => descriptors[route.key].route.name}
    />
  );
};

const App = () => {
  // Initialize services on app startup
  /* useEffect(() => {
    const initializeServices = async () => {
      try {
        // Configure Google Sign-In for com.prepfy.qna
        AuthService.configureGoogleSignIn();

        // Check if Google Sign-In is working
        try {
          const isSignedIn = await GoogleSignin.isSignedIn();
          console.log('[App] Google Sign-In status check: isSignedIn =', isSignedIn);

          if (isSignedIn) {
            const currentUser = GoogleSignin.getCurrentUser();
            // Try to get current user info - getCurrentUser is not a promise in newer versions
            console.log('[App] Current Google user:', currentUser ? 'Found' : 'Not found');
          }
        } catch (signInError) {
          console.error('[App] Error checking Google Sign-In status:', signInError);
        }

        // Restore authentication state (Google ID token)
        const authRestored = await AuthService.restoreAuthState();
        console.log('[App] Auth state restoration:', authRestored ? 'Success' : 'No stored auth');



        // Hide boot splash screen
        await BootSplash.hide({ fade: true });
        console.log('[App] BootSplash has been hidden successfully');

        // Check if user is signed in and initialize real-time sync
        const userInfo = GoogleSignin.getCurrentUser();
        if (userInfo && userInfo.user) {
          console.log('[App] Found signed-in user, initializing real-time sync');

          // Initialize real-time sync service for already logged-in user
          console.log('[App] Initializing real-time sync service for logged-in user:', userInfo.user.id);

          // Set app active state before initializing sync service
          RealTimeSyncService.setAppActive(true);
          RealTimeSyncService.initialize(userInfo.user.id);
        } else {
          console.log('[App] No signed-in user found');

          // Check if we have user data in AsyncStorage (user might be logged in via our system)
          try {
            const storedUser = await AsyncStorage.getItem('user');
            if (storedUser) {
              const userData = JSON.parse(storedUser);
              if (userData && userData.id && typeof userData.id === 'string' && userData.id.trim() !== '') {
                console.log('[App] Found stored user data, initializing real-time sync service:', userData.id);

                // Set app active state before initializing sync service
                RealTimeSyncService.setAppActive(true);
                RealTimeSyncService.initialize(userData.id);
              } else {
                console.log('[App] Stored user data exists but no valid user ID found');
                // Ensure sync service is stopped if no valid user
                RealTimeSyncService.stop();
              }
            } else {
              console.log('[App] No stored user data found, ensuring sync service is stopped');
              // Ensure sync service is stopped if no user data
              RealTimeSyncService.stop();
            }
          } catch (error) {
            console.error('[App] Error checking stored user data:', error);
            // Ensure sync service is stopped on error
            RealTimeSyncService.stop();
          }
        }
      } catch (error) {
        console.error('Error initializing services:', error);
      }
    };

    initializeServices();
  }, []); */

  // Handle app state changes for real-time sync service
  /* useEffect(() => {
    const handleAppStateChange = (nextAppState) => {
      console.log('[App] App state changed to:', nextAppState);

      if (nextAppState === 'active') {
        RealTimeSyncService.setAppActive(true);
      } else if (nextAppState === 'background' || nextAppState === 'inactive') {
        RealTimeSyncService.setAppActive(false);
      }
    };

    const subscription = AppState.addEventListener('change', handleAppStateChange);

    // Set initial app state
    RealTimeSyncService.setAppActive(AppState.currentState === 'active');

    return () => {
      subscription?.remove();
    };
  }, []); */

  return (
    <PaperProvider>
      <PurchaseProvider>
        <AICreditProvider>
          <NavigationContainer>              <Tab.Navigator
                screenOptions={{ headerShown: false }}
                tabBar={props => <CustomTabBar {...props} />}>
                <Tab.Screen
                  name="Home"
                  component={HomeScreen}
                  options={{
                    tabBarIcon: ({ color, size }) => (
                      <Icon name="home" size={size} color={color} />
                    ),
                  }}
                />
                <Tab.Screen
                  name="Tutorial"
                  component={CopilotTestScreen}
                  options={{
                    tabBarIcon: ({ color, size }) => (
                      <Icon name="help-circle" size={size} color={color} />
                    ),
                  }}
                />
                <Tab.Screen
                  name="Settings"
                  component={SettingsScreen}
                  options={{
                    tabBarIcon: ({ color, size }) => (
                      <Icon name="cog" size={size} color={color} />
                    ),
                  }}
                />
              </Tab.Navigator>
            </NavigationContainer>
          </AICreditProvider>
        </PurchaseProvider>
    </PaperProvider>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default function AppWrapper() {
  return (
    <CopilotProvider>
      <App />
    </CopilotProvider>
  );
}