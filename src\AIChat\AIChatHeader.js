import React, { useState } from 'react';
import { View, StyleSheet } from 'react-native';
import { Appbar, useTheme } from 'react-native-paper';
import { useNavigation } from '@react-navigation/native';
import AICreditBadge from '../components/AICreditBadge';
import AICreditModal from '../components/AICreditModal';

/**
 * Header component for the AI Chat screen
 * Displays the title and AI credit badge
 */
const AIChatHeader = ({ title }) => {
  const { colors } = useTheme();
  const navigation = useNavigation();
  const [creditModalVisible, setCreditModalVisible] = useState(false);
  
  return (
    <>
      <Appbar.Header elevated>
        <Appbar.BackAction onPress={() => navigation.goBack()} />
        <Appbar.Content title={title || "Ask AI"} />
        <AICreditBadge onPress={() => setCreditModalVisible(true)} />
      </Appbar.Header>
      
      <AICreditModal 
        visible={creditModalVisible}
        onDismiss={() => setCreditModalVisible(false)}
      />
    </>
  );
};

const styles = StyleSheet.create({});

export default AIChatHeader;
