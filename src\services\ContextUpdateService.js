import AsyncStorage from '@react-native-async-storage/async-storage';
import RealTimeSyncService from './RealTimeSyncService';
import userProgressSyncService from './UserProgressSyncService';

/**
 * ContextUpdateService - <PERSON>les React Context Updates
 *
 * Responsibilities:
 * - Update React contexts with user data after login/logout
 * - Handle context registration and coordination between different contexts
 * - Manage QnA reload triggers with correct premium status
 * - Coordinate context clearing on logout
 *
 * This service focuses solely on context management and does NOT handle MongoDB synchronization.
 * All MongoDB sync operations are handled by RealTimeSyncService.
 */
class ContextUpdateService {
  static contextRefs = {
    userProgress: null,
    quizResult: null,
    purchase: null,
    aiCredit: null,
    aiChat: null,
    login: null,
    exam: null,
    qna: null
  };

  /**
   * Register context references for updates
   */
  static registerContexts(contexts) {
    console.log('[ContextUpdateService] Registering contexts:', Object.keys(contexts));
    this.contextRefs = { ...this.contextRefs, ...contexts };
  }

  /**
   * Update all contexts with user data after successful login
   */
  static async updateAllContextsAfterLogin(userData) {
    try {
      console.log('[ContextUpdateService] ===== STARTING CONTEXT UPDATES =====');
      console.log('[ContextUpdateService] Updating contexts with user data for user:', userData.id);

      // Update UserProgressContext
      await this.updateUserProgressContext(userData.progress);

      // Update QuizResultContext
      await this.updateQuizResultContext(userData.quizResults);

      // Update PurchaseContext
      await this.updatePurchaseContext(userData.purchases);

      // Update AICreditContext
      await this.updateAICreditContext(userData.aiCredits);

      // Update AIChatContext
      await this.updateAIChatContext(userData.aiChats);

      // Update LoginContext
      await this.updateLoginContext(userData);

      // Initialize real-time sync service for the logged-in user
      console.log('[ContextUpdateService] Initializing real-time sync service...');
      RealTimeSyncService.initialize(userData.id);

      // Initialize user progress sync service for the logged-in user
      console.log('[ContextUpdateService] Initializing user progress sync service...');
      userProgressSyncService.initializeForUser(userData.id);

      // Trigger QnA reload with premium status after context updates
      await this.triggerQnAReloadAfterLogin(userData);

      console.log('[ContextUpdateService] ===== ALL CONTEXTS UPDATED SUCCESSFULLY =====');
      return true;
    } catch (error) {
      console.error('[ContextUpdateService] Error updating contexts:', error);
      return false;
    }
  }

  /**
   * Trigger QnA reload with correct premium status after login
   */
  static async triggerQnAReloadAfterLogin(userData) {
    try {
      console.log('[ContextUpdateService] ===== TRIGGERING QNA RELOAD AFTER LOGIN =====');

      // Get the current selected exam from context
      if (this.contextRefs.exam && this.contextRefs.exam.selectedExam) {
        const selectedExam = this.contextRefs.exam.selectedExam;
        console.log('[ContextUpdateService] Selected exam for QnA reload:', selectedExam.exam_code);

        // Check if user has premium access to this exam
        let hasPremiumAccess = false;
        if (this.contextRefs.purchase && this.contextRefs.purchase.isSubscriptionActive) {
          hasPremiumAccess = this.contextRefs.purchase.isSubscriptionActive(selectedExam?.exam_code);
        }

        console.log('[ContextUpdateService] Premium access check:', {
          examId: selectedExam.id,
          examCode: selectedExam.exam_code,
          hasPremiumAccess: hasPremiumAccess
        });

        // Trigger QnA reload with correct premium status
        if (this.contextRefs.qna && this.contextRefs.qna.loadQnA) {
          const is_free = !hasPremiumAccess;
          console.log('[ContextUpdateService] Triggering QnA reload with is_free:', is_free);

          // Add a small delay to ensure contexts are fully updated
          await new Promise(resolve => setTimeout(resolve, 500));

          await this.contextRefs.qna.loadQnA(selectedExam.exam_code, is_free, selectedExam.id);
          console.log('[ContextUpdateService] QnA reload completed successfully');
        } else {
          console.warn('[ContextUpdateService] QnA context not available for reload');
        }
      } else {
        console.log('[ContextUpdateService] No selected exam found, skipping QnA reload');
      }
    } catch (error) {
      console.error('[ContextUpdateService] Error triggering QnA reload:', error);
    }
  }

  /**
   * Update UserProgressContext with progress data
   */
  static async updateUserProgressContext(progressData) {
    try {
      console.log('[ContextUpdateService] Updating UserProgressContext:', {
        coursesCount: progressData?.courses?.length || 0,
        lastActivity: progressData?.lastActivity,
        progressId: progressData?.progressId
      });

      if (this.contextRefs.userProgress && this.contextRefs.userProgress.updateProgress) {
        await this.contextRefs.userProgress.updateProgress(progressData);
        console.log('[ContextUpdateService] UserProgressContext updated successfully');
      } else {
        console.warn('[ContextUpdateService] UserProgressContext not available for update');
      }
    } catch (error) {
      console.error('[ContextUpdateService] Error updating UserProgressContext:', error);
    }
  }

  /**
   * Update QuizResultContext with quiz results data
   */
  static async updateQuizResultContext(quizResults) {
    try {
      console.log('[ContextUpdateService] Updating QuizResultContext:', {
        quizResultsCount: quizResults?.length || 0
      });

      if (this.contextRefs.quizResult && this.contextRefs.quizResult.updateQuizResults) {
        await this.contextRefs.quizResult.updateQuizResults(quizResults);
        console.log('[ContextUpdateService] QuizResultContext updated successfully');
      } else {
        console.warn('[ContextUpdateService] QuizResultContext not available for update');
      }
    } catch (error) {
      console.error('[ContextUpdateService] Error updating QuizResultContext:', error);
    }
  }

  /**
   * Update PurchaseContext with purchases data
   */
  static async updatePurchaseContext(purchases) {
    try {
      console.log('[ContextUpdateService] Updating PurchaseContext:', {
        purchasesCount: purchases?.length || 0,
        purchases: purchases
      });

      if (this.contextRefs.purchase && this.contextRefs.purchase.updatePurchases) {
        await this.contextRefs.purchase.updatePurchases(purchases);
        console.log('[ContextUpdateService] PurchaseContext updated successfully');
      } else {
        console.warn('[ContextUpdateService] PurchaseContext not available for update');
      }
    } catch (error) {
      console.error('[ContextUpdateService] Error updating PurchaseContext:', error);
    }
  }



  /**
   * Update AICreditContext with AI credits data
   */
  static async updateAICreditContext(aiCredits) {
    try {
      console.log('[ContextUpdateService] Updating AICreditContext:', {
        totalCredits: aiCredits?.totalCredits || 0,
        usedCredits: aiCredits?.usedCredits || 0,
        availableCredits: aiCredits?.availableCredits || 0,
        transactionsCount: aiCredits?.transactions?.length || 0
      });

      if (this.contextRefs.aiCredit && this.contextRefs.aiCredit.updateCredits) {
        await this.contextRefs.aiCredit.updateCredits(aiCredits);
        console.log('[ContextUpdateService] AICreditContext updated successfully');
      } else {
        console.warn('[ContextUpdateService] AICreditContext not available for update');
      }
    } catch (error) {
      console.error('[ContextUpdateService] Error updating AICreditContext:', error);
    }
  }



  /**
   * Update AIChatContext with AI chats data
   */
  static async updateAIChatContext(aiChats) {
    try {
      console.log('[ContextUpdateService] Updating AIChatContext:', {
        aiChatsCount: aiChats?.length || 0
      });

      if (this.contextRefs.aiChat && this.contextRefs.aiChat.loadAIChatFromAPI) {
        this.contextRefs.aiChat.loadAIChatFromAPI(aiChats);
        console.log('[ContextUpdateService] AIChatContext updated successfully');
      } else {
        console.warn('[ContextUpdateService] AIChatContext not available for update');
      }
    } catch (error) {
      console.error('[ContextUpdateService] Error updating AIChatContext:', error);
    }
  }

  /**
   * Update LoginContext with user profile data
   */
  static async updateLoginContext(userData) {
    try {
      console.log('[ContextUpdateService] Updating LoginContext:', {
        userId: userData.id,
        googleId: userData.googleId,
        email: userData.email,
        displayName: userData.displayName,
        hasProfilePicture: !!userData.profilePicture
      });

      if (this.contextRefs.login && this.contextRefs.login.updateUserProfile) {
        const profileData = {
          _id: userData._id,
          id: userData.id,
          googleId: userData.googleId,
          email: userData.email,
          displayName: userData.displayName,
          name: userData.name,
          profilePicture: userData.profilePicture,
          photo: userData.photo,
          createdAt: userData.createdAt,
          updatedAt: userData.updatedAt
        };

        await this.contextRefs.login.updateUserProfile(profileData);
        await this.contextRefs.login.setIsLoggedIn(true);
        console.log('[ContextUpdateService] LoginContext updated successfully');
      } else {
        console.warn('[ContextUpdateService] LoginContext not available for update');
      }
    } catch (error) {
      console.error('[ContextUpdateService] Error updating LoginContext:', error);
    }
  }

  /**
   * Clear all contexts on logout
   */
  static async clearAllContexts() {
    try {
      console.log('[ContextUpdateService] ===== CLEARING ALL CONTEXTS =====');

      // Stop real-time sync service
      console.log('[ContextUpdateService] Stopping real-time sync service...');
      RealTimeSyncService.stop();

      // Stop user progress sync service
      console.log('[ContextUpdateService] Stopping user progress sync service...');
      userProgressSyncService.handleUserLogout();

      // Clear UserProgressContext
      if (this.contextRefs.userProgress && this.contextRefs.userProgress.clearProgress) {
        await this.contextRefs.userProgress.clearProgress();
        console.log('[ContextUpdateService] UserProgressContext cleared');
      }

      // Clear QuizResultContext
      if (this.contextRefs.quizResult && this.contextRefs.quizResult.clearQuizResults) {
        await this.contextRefs.quizResult.clearQuizResults();
        console.log('[ContextUpdateService] QuizResultContext cleared');
      }

      // Clear PurchaseContext
      if (this.contextRefs.purchase && this.contextRefs.purchase.clearPurchases) {
        await this.contextRefs.purchase.clearPurchases();
        console.log('[ContextUpdateService] PurchaseContext cleared');
      }

      // Clear AICreditContext
      if (this.contextRefs.aiCredit && this.contextRefs.aiCredit.clearCredits) {
        await this.contextRefs.aiCredit.clearCredits();
        console.log('[ContextUpdateService] AICreditContext cleared');
      }

      // Clear AIChatContext
      if (this.contextRefs.aiChat && this.contextRefs.aiChat.clearAllChatHistories) {
        this.contextRefs.aiChat.clearAllChatHistories();
        console.log('[ContextUpdateService] AIChatContext cleared');
      }

      // Clear LoginContext
      if (this.contextRefs.login && this.contextRefs.login.logout) {
        await this.contextRefs.login.logout();
        console.log('[ContextUpdateService] LoginContext cleared');
      }

      console.log('[ContextUpdateService] ===== ALL CONTEXTS CLEARED =====');
      return true;
    } catch (error) {
      console.error('[ContextUpdateService] Error clearing contexts:', error);
      return false;
    }
  }

  /**
   * Restore contexts from AsyncStorage on app startup
   */
  static async restoreContextsFromStorage() {
    try {
      console.log('[ContextUpdateService] Restoring contexts from AsyncStorage');

      // Get stored data
      const [
        userProgressData,
        quizResultsData,
        purchasesData,
        aiCreditsData,
        aiChatsData,
        userProfileData
      ] = await Promise.all([
        AsyncStorage.getItem('user_progress'),
        AsyncStorage.getItem('quiz_results'),
        AsyncStorage.getItem('user_purchases'),
        AsyncStorage.getItem('ai_credits'),
        AsyncStorage.getItem('ai_chats'),
        AsyncStorage.getItem('user_profile')
      ]);

      // Parse and restore data
      if (userProgressData) {
        await this.updateUserProgressContext(JSON.parse(userProgressData));
      }

      if (quizResultsData) {
        await this.updateQuizResultContext(JSON.parse(quizResultsData));
      }

      if (purchasesData) {
        await this.updatePurchaseContext(JSON.parse(purchasesData));
      }

      if (aiCreditsData) {
        await this.updateAICreditContext(JSON.parse(aiCreditsData));
      }

      if (aiChatsData) {
        await this.updateAIChatContext(JSON.parse(aiChatsData));
      }

      if (userProfileData) {
        await this.updateLoginContext(JSON.parse(userProfileData));
      }

      console.log('[ContextUpdateService] Contexts restored from AsyncStorage');
      return true;
    } catch (error) {
      console.error('[ContextUpdateService] Error restoring contexts:', error);
      return false;
    }
  }
}

export default ContextUpdateService;
