import React, { Component } from "react";
import { Image, View } from "react-native";
import FastImage from 'react-native-fast-image';

export default class ScaledImage extends Component {
    constructor(props) {
        super(props);
        this.state = { 
            source: props.uri ? { uri: props.uri } : null,
            width: props.width || 0,
            height: props.height || 0,
            loaded: false
        };
    }

    componentDidMount() {
        // Only try to get size if URI is provided
        if (this.props.uri) {
            Image.getSize(
                this.props.uri, 
                (width, height) => {
                    if (this.props.width && !this.props.height) {
                        this.setState({
                            width: this.props.width,
                            height: height * (this.props.width / width),
                            loaded: true
                        });
                    } else if (!this.props.width && this.props.height) {
                        this.setState({
                            width: width * (this.props.height / height),
                            height: this.props.height,
                            loaded: true
                        });
                    } else {
                        this.setState({ 
                            width: width, 
                            height: height,
                            loaded: true 
                        });
                    }
                },
                (error) => {
                    console.error('Error loading image:', error);
                    this.setState({ 
                        width: this.props.width || 100, 
                        height: this.props.height || 100,
                        loaded: false
                    });
                }
            );
        }
    }

    componentDidUpdate(prevProps) {
        // Update if URI changes
        if (prevProps.uri !== this.props.uri && this.props.uri) {
            this.setState({ 
                source: { uri: this.props.uri },
                loaded: false
            });
            
            Image.getSize(
                this.props.uri, 
                (width, height) => {
                    if (this.props.width && !this.props.height) {
                        this.setState({
                            width: this.props.width,
                            height: height * (this.props.width / width),
                            loaded: true
                        });
                    } else if (!this.props.width && this.props.height) {
                        this.setState({
                            width: width * (this.props.height / height),
                            height: this.props.height,
                            loaded: true
                        });
                    } else {
                        this.setState({ 
                            width: width, 
                            height: height,
                            loaded: true 
                        });
                    }
                },
                (error) => {
                    console.error('Error loading image:', error);
                    this.setState({ 
                        width: this.props.width || 100, 
                        height: this.props.height || 100,
                        loaded: false
                    });
                }
            );
        }
    }

    render() {
        // Don't render anything if no source
        if (!this.state.source) {
            return <View style={{ width: this.props.width || 0, height: this.props.height || 0 }} />;
        }

        return (
            <FastImage
                source={this.state.source}
                style={{ 
                    height: this.state.height, 
                    width: this.state.width,
                    opacity: this.state.loaded ? 1 : 0.5 // Show faded if not fully loaded
                }}
                onError={() => {
                    console.log('FastImage error loading:', this.props.uri);
                }}
            />
        );
    }
}