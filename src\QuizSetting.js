import React, { useState, useMemo, useEffect } from 'react';
import { View, ScrollView, StyleSheet, TouchableOpacity, Alert } from 'react-native';
import {
  Text,
  Appbar,
  useTheme,
  Button,
  Portal,
  Modal,
} from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { useNavigation, useRoute } from '@react-navigation/native';
import CalendarPicker from 'react-native-calendar-picker';
import { useQuizSetting } from './store/QuizSettingContext';
import DateCalendarInput from '../src/components/DateCalendarInput';
import { useExamContext } from './store/ExamContext';
import FixedFontButton from './components/FixedFontButton';
import CustomAppbarContent from './components/CustomAppbarContent';

const QuizSetting = () => {
  const route = useRoute();
  const navigation = useNavigation();
  const { colors } = useTheme();
  const { appliedFilters = { dateRange: {} }, setAppliedFilters } = useQuizSetting();
  const preselectedSubject = route.params?.preselectedSubject;

  // State management
  const [hasModifiedSelection, setHasModifiedSelection] = useState(false);

  const [selectedSubjects, setSelectedSubjects] = useState(
    route.params?.selectedSubjects || appliedFilters.subjects || []
  );

  const parseDateParam = (param) => {
    if (!param) return null;
    const date = new Date(param);
    return isNaN(date.getTime()) ? null : date;
  };

  const [selectedStartDate, setSelectedStartDate] = useState(() =>
    parseDateParam(route.params?.initialStartDate) || appliedFilters.dateRange?.startDate || null
  );
  const [selectedEndDate, setSelectedEndDate] = useState(() =>
    parseDateParam(route.params?.initialEndDate) || appliedFilters.dateRange?.endDate || null
  );
  const [questionLimit, setQuestionLimit] = useState(appliedFilters.questionLimit || 'All');
  const [onlyIncorrect, setOnlyIncorrect] = useState(appliedFilters.onlyIncorrect || false);
  const [onlyFlagged, setOnlyFlagged] = useState(appliedFilters.onlyFlagged || false);
  const [onlyBookmarked, setOnlyBookmarked] = useState(appliedFilters.onlyBookmarked || false);
  const [onlyUnbrowsed, setOnlyUnbrowsed] = useState(appliedFilters.onlyUnbrowsed || false);
  const [showQuestionLimitPicker, setShowQuestionLimitPicker] = useState(false);

  const { selectedExam } = useExamContext();

  const uniqueSubjects = useMemo(() => {
    return selectedExam?.subjects?.map((subject, index) => ({
      name: typeof subject === 'object' ? subject.name : String(subject),
      code: typeof subject === 'object' ? Number(subject.code) : index + 1
    })) || [];
  }, [selectedExam?.subjects]);


  const questionLimitOptions = [
    { label: 'Up to 10', value: 10 },
    { label: 'Up to 20', value: 20 },
    { label: 'Up to 30', value: 30 },
    { label: 'Up to 45', value: 45 },
    { label: 'All Questions', value: 'All' }
  ];

  const toggleSubject = (code) => {
    setHasModifiedSelection(true);
    setSelectedSubjects(prev => prev.includes(code)
      ? prev.filter(s => s !== code)
      : [...prev, code]
    );
  };

  const handleResetDates = () => {
    setSelectedStartDate(null);
    setSelectedEndDate(null);
  };

  const [isLoading, setIsLoading] = useState(false);

  const applyFilters = () => {
    if (selectedSubjects.length === 0) {
      Alert.alert("No Subjects Selected", "Please select at least one subject.");
      return;
    }

    setIsLoading(true);

    // Apply filters and navigate with a slight delay to show loading state
    setAppliedFilters({
      subjects: selectedSubjects,
      dateRange: { startDate: selectedStartDate, endDate: selectedEndDate },
      questionLimit,
      onlyIncorrect,
      onlyFlagged,
      onlyBookmarked,
      onlyUnbrowsed
    });

    // Add a small delay to show the loading animation
    setTimeout(() => {
      navigation.navigate('QuizMode');
      setIsLoading(false);
    }, 500);
  };

  const formatDate = (date) => date ?
    new Date(date).toLocaleDateString('en-US', { month: 'short', day: 'numeric', year: 'numeric' })
    : 'Select Date';

  const onDateChange = (date, type) => {
    type === 'START_DATE' ? setSelectedStartDate(date) : setSelectedEndDate(date);
  };

  return (
    <View style={{ flex: 1, backgroundColor: colors.background }}>
      <Appbar.Header elevated>
        <Appbar.BackAction onPress={() => navigation.goBack()} />
        <CustomAppbarContent title="Quiz Settings" />
      </Appbar.Header>

      <ScrollView
        contentContainerStyle={styles.container}
        showsVerticalScrollIndicator={false}
      >
        {/* Subjects Section */}
        <View style={styles.section}>
          <Text variant="titleSmall" style={[styles.sectionTitle, { color: colors.onSurface }]}>
            SELECT SUBJECTS
          </Text>
          <View style={styles.chipContainer}>
            {uniqueSubjects.map((subject, index) => (
              <TouchableOpacity
                key={index}
                style={[
                  styles.subjectItem,
                  {
                    backgroundColor: colors.surface,
                    borderColor: selectedSubjects.includes(subject.code) ? colors.primary : colors.outlineVariant
                  }
                ]}
                onPress={() => toggleSubject(subject.code)}
              >
                <Text
                  style={[
                    styles.subjectText,
                    { color: selectedSubjects.includes(subject.code) ? colors.primary : colors.onSurface }
                  ]}
                >
                  {subject.name}
                </Text>
                <Icon
                  name={selectedSubjects.includes(subject.code) ? "checkbox-marked" : "checkbox-blank-outline"}
                  size={24}
                  color={selectedSubjects.includes(subject.code) ? colors.primary : colors.outline}
                />
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Date Range Section */}
        {/* <View style={styles.section}>
          <DateCalendarInput
            startDate={selectedStartDate}
            endDate={selectedEndDate}
            onDateChange={onDateChange}
          />
        </View> */}

        {/* Question Limit Section */}
        <View style={styles.section}>
          <Text variant="titleSmall" style={[styles.sectionTitle, { color: colors.onSurface }]}>
            QUESTION LIMIT
          </Text>
          <TouchableOpacity
            style={[styles.filterButton, { backgroundColor: colors.surface }]}
            onPress={() => setShowQuestionLimitPicker(true)}
          >
            <Icon name="counter" size={20} color={colors.primary} />
            <Text variant="bodyMedium" style={[styles.buttonText, { color: colors.onSurface }]}>
              {questionLimit === "All" ? "All Questions" : `Up to ${questionLimit} Questions`}
            </Text>
            <Icon name="chevron-right" size={20} color={colors.outline} />
          </TouchableOpacity>
        </View>

        {/* Quiz Filters Section */}
        <View style={styles.section}>
          <Text variant="titleSmall" style={[styles.sectionTitle, { color: colors.onSurface }]}>
            QUIZ FILTERS
          </Text>
          <View style={styles.chipContainer}>
            {[
              {
                label: 'Last incorrect',
                state: onlyIncorrect,
                setter: setOnlyIncorrect,
                icon: 'alert-circle',
              },
              {
                label: 'Bookmarked',
                state: onlyBookmarked,
                setter: setOnlyBookmarked,
                icon: 'bookmark',
              },
              /* {
                label: 'Previously Flagged',
                state: onlyFlagged,
                setter: setOnlyFlagged,
                icon: 'flag',
              }, */
              {
                label: 'Unbrowsed',
                state: onlyUnbrowsed,
                setter: setOnlyUnbrowsed,
                icon: 'eye-off',
              },
            ].map((filter, index) => {
              const isUnbrowsed = filter.label === 'Unbrowsed';
              const areDatesSelected = selectedStartDate !== null || selectedEndDate !== null;
              const isDisabled = isUnbrowsed && areDatesSelected;

              return (
                <TouchableOpacity
                  key={index}
                  style={[
                    styles.filterItem,
                    {
                      backgroundColor: colors.surface,
                      borderColor: isDisabled ? colors.outline : (filter.state ? colors.primary : colors.outlineVariant),
                      opacity: isDisabled ? 0.5 : 1,
                    }
                  ]}
                  onPress={() => !isDisabled && filter.setter(!filter.state)}
                  disabled={isDisabled}
                >
                  <View style={styles.filterContent}>
                    <Icon
                      name={filter.icon}
                      size={20}
                      color={isDisabled ? colors.outline : (filter.state ? colors.primary : colors.onSurface)}
                      style={styles.filterIcon}
                    />
                    <Text
                      style={[
                        styles.filterText,
                        {
                          color: isDisabled ? colors.outline : (filter.state ? colors.primary : colors.onSurface)
                        }
                      ]}
                    >
                      {filter.label}
                    </Text>
                  </View>
                  <Icon
                    name={filter.state ? "checkbox-marked" : "checkbox-blank-outline"}
                    size={24}
                    color={isDisabled ? colors.outline : (filter.state ? colors.primary : colors.outline)}
                  />
                </TouchableOpacity>
              );
            })}
          </View>
        </View>
      </ScrollView>

      {/* Sticky Start Button */}
      <View style={[styles.startButton, { backgroundColor: colors.surface, borderTopColor: colors.surfaceVariant, borderTopWidth: 1 }]}>
        <FixedFontButton
          mode="contained"
          style={styles.startButtonStyle}
          contentStyle={styles.startButtonContent}
          labelStyle={{ color: '#FFFFFF', fontWeight: '600', fontSize: 16 }}
          onPress={() => applyFilters()}
          disabled={isLoading}
          loading={isLoading}
          theme={{ colors: { primary: colors.primary } }}
        >
          {isLoading ? 'Starting...' : 'Start Quiz'}
        </FixedFontButton>
      </View>

      {/* Overlay to disable interactions during loading */}
      {isLoading && (
        <View style={styles.loadingOverlay} />
      )}

      {/* Question Limit Modal */}
      <Portal>
        <Modal
          visible={showQuestionLimitPicker}
          onDismiss={() => setShowQuestionLimitPicker(false)}
          contentContainerStyle={[styles.modalContainer, { backgroundColor: colors.surface }]}
        >
          <View style={styles.modalHeader}>
            <Text variant="titleMedium" style={{ color: colors.onSurface }}>
              Select Question Limit
            </Text>
            <Button onPress={() => setShowQuestionLimitPicker(false)}>Done</Button>
          </View>
          <View style={styles.optionsContainer}>
            {questionLimitOptions.map((option) => (
              <TouchableOpacity
                key={option.value}
                style={[
                  styles.optionButton,
                  {
                    backgroundColor: questionLimit === option.value ? colors.primaryContainer : colors.surface,
                    borderColor: colors.outlineVariant
                  }
                ]}
                onPress={() => setQuestionLimit(option.value)}
              >
                <Text
                  variant="bodyMedium"
                  style={{ color: questionLimit === option.value ? colors.primary : colors.onSurface }}
                >
                  {option.label}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </Modal>
      </Portal>

    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 16,
    paddingBottom: 88,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    marginBottom: 12,
    letterSpacing: 0.5,
  },
  chipContainer: {
    gap: 8,
  },
  subjectItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderRadius: 8,
    borderWidth: 1,
    padding: 16,
  },
  subjectText: {
    flex: 1,
    marginRight: 12,
  },
  filterButton: {
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: 8,
    padding: 16,
    gap: 12,
  },
  buttonText: {
    flex: 1,
  },
  filterItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderRadius: 8,
    borderWidth: 1,
    padding: 16,
  },
  filterContent: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  filterIcon: {
    marginRight: 12,
  },
  filterText: {
    fontSize: 15,
    flex: 1,
  },
  startButton: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    padding: 16,
  },
  startButtonStyle: {
    borderRadius: 8,
    width: '100%',
  },
  startButtonContent: {
    height: 48,
  },
  modalContainer: {
    padding: 20,
    margin: 20,
    borderRadius: 16,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
    paddingHorizontal: 8, // Add horizontal padding
  },
  optionsContainer: {
    gap: 8,
  },
  optionButton: {
    borderRadius: 8,
    borderWidth: 1,
    padding: 16,
    width: '100%', // Ensure buttons take full width
  },
  loadingOverlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
    zIndex: 1000,
    elevation: 5,
  },
});

export default QuizSetting;