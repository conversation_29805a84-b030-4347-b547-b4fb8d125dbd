/**
 * Mock AdMob service for AI credit integration
 * This is a placeholder implementation that will be replaced with actual AdMob integration
 */
class AdService {
  // Test ad unit IDs
  static BANNER_AD_UNIT_ID = 'ca-app-pub-3940256099942544/6300978111'; // Test banner ad ID
  static INTERSTITIAL_AD_UNIT_ID = 'ca-app-pub-3940256099942544/1033173712'; // Test interstitial ad ID
  static REWARDED_AD_UNIT_ID = 'ca-app-pub-3940256099942544/5224354917'; // Test rewarded ad ID
  
  /**
   * Initialize the AdMob service
   * @returns {Promise<void>}
   */
  static async initialize() {
    console.log('AdService: Initializing mock AdMob service');
    return Promise.resolve();
  }
  
  /**
   * Load a rewarded ad
   * @returns {Promise<boolean>} - Whether the ad was loaded successfully
   */
  static async loadRewardedAd() {
    console.log('AdService: Loading mock rewarded ad');
    // Simulate ad loading delay
    await new Promise(resolve => setTimeout(resolve, 1000));
    return Promise.resolve(true);
  }
  
  /**
   * Show a rewarded ad
   * @returns {Promise<Object>} - Reward object with type and amount
   */
  static async showRewardedAd() {
    console.log('AdService: Showing mock rewarded ad');
    
    // Simulate ad viewing delay
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Simulate reward
    return Promise.resolve({
      type: 'ai_credits',
      amount: 5
    });
  }
  
  /**
   * Load an interstitial ad
   * @returns {Promise<boolean>} - Whether the ad was loaded successfully
   */
  static async loadInterstitialAd() {
    console.log('AdService: Loading mock interstitial ad');
    // Simulate ad loading delay
    await new Promise(resolve => setTimeout(resolve, 1000));
    return Promise.resolve(true);
  }
  
  /**
   * Show an interstitial ad
   * @returns {Promise<boolean>} - Whether the ad was shown successfully
   */
  static async showInterstitialAd() {
    console.log('AdService: Showing mock interstitial ad');
    // Simulate ad viewing delay
    await new Promise(resolve => setTimeout(resolve, 1500));
    return Promise.resolve(true);
  }
  
  /**
   * Check if a rewarded ad is ready to show
   * @returns {Promise<boolean>} - Whether a rewarded ad is ready
   */
  static async isRewardedAdReady() {
    console.log('AdService: Checking if mock rewarded ad is ready');
    return Promise.resolve(true);
  }
  
  /**
   * Check if an interstitial ad is ready to show
   * @returns {Promise<boolean>} - Whether an interstitial ad is ready
   */
  static async isInterstitialAdReady() {
    console.log('AdService: Checking if mock interstitial ad is ready');
    return Promise.resolve(true);
  }
}

export default AdService;
