import AsyncStorage from '@react-native-async-storage/async-storage';
import apiClient from './ApiClient';
import { USER_PROGRESS_SYNC_WINDOW, AI_CHAT_SYNC_WINDOW, ENABLE_REALTIME_SYNC } from '@env';

/**
 * Real-time MongoDB synchronization service
 * Handles periodic syncing of user data to MongoDB with configurable intervals
 */
class RealTimeSyncService {
  static syncIntervals = {
    userProgress: null,
    aiChat: null
  };

  static lastSyncData = {
    userProgress: null,
    aiChat: null,
    quizResults: null
  };

  static isAppActive = true;
  static userId = null;

  // Environment variable defaults (in seconds)
  static USER_PROGRESS_SYNC_WINDOW = parseInt(USER_PROGRESS_SYNC_WINDOW) || 10;
  static AI_CHAT_SYNC_WINDOW = parseInt(AI_CHAT_SYNC_WINDOW) || 10;
  static SYNC_ENABLED = ENABLE_REALTIME_SYNC === 'true' || ENABLE_REALTIME_SYNC === true;

  /**
   * Initialize the real-time sync service
   */
  static initialize(userId) {
    console.log('[RealTimeSyncService] ===== INITIALIZING REAL-TIME SYNC SERVICE =====');
    console.log('[RealTimeSyncService] Initializing real-time sync service for user:', userId);
    console.log('[RealTimeSyncService] Current app active state:', this.isAppActive);
    console.log('[RealTimeSyncService] Sync enabled via environment:', this.SYNC_ENABLED);

    // Always stop existing intervals first
    this.stop();

    // Check if sync is disabled via environment variable
    if (!this.SYNC_ENABLED) {
      console.log('[RealTimeSyncService] ⚠️ RealTimeSyncService is DISABLED via ENABLE_REALTIME_SYNC environment variable');
      console.log('[RealTimeSyncService] ===== SYNC SERVICE INITIALIZATION SKIPPED (DISABLED) =====');
      return;
    }

    // Only proceed if we have a valid userId
    if (!userId || typeof userId !== 'string' || userId.trim() === '') {
      console.warn('[RealTimeSyncService] No valid userId provided, sync service will not start');
      console.log('[RealTimeSyncService] ===== SYNC SERVICE INITIALIZATION SKIPPED =====');
      return;
    }

    this.userId = userId.trim();

    // Only start sync intervals if app is active and we have a user
    if (this.isAppActive) {
      console.log('[RealTimeSyncService] Starting sync intervals for logged-in user...');
      this.startUserProgressSync();
      this.startAIChatSync();
      console.log('[RealTimeSyncService] Sync intervals started successfully');
    } else {
      console.log('[RealTimeSyncService] App is not active, sync intervals will start when app becomes active');
    }

    console.log('[RealTimeSyncService] Sync intervals configured:', {
      userProgressWindow: this.USER_PROGRESS_SYNC_WINDOW,
      aiChatWindow: this.AI_CHAT_SYNC_WINDOW,
      userId: this.userId,
      isAppActive: this.isAppActive,
      intervalsActive: {
        userProgress: !!this.syncIntervals.userProgress,
        aiChat: !!this.syncIntervals.aiChat
      }
    });
    console.log('[RealTimeSyncService] ===== SYNC SERVICE INITIALIZATION COMPLETE =====');
  }

  /**
   * Stop all sync intervals and clear user data
   */
  static stop() {
    console.log('[RealTimeSyncService] Stopping all sync intervals and clearing user data');

    this.stopSyncIntervals();

    this.userId = null;
    this.lastSyncData = {
      userProgress: null,
      aiChat: null,
      quizResults: null
    };
  }

  /**
   * Stop sync intervals only (without clearing user data)
   */
  static stopSyncIntervals() {
    console.log('[RealTimeSyncService] Stopping sync intervals');

    Object.keys(this.syncIntervals).forEach(key => {
      if (this.syncIntervals[key]) {
        console.log(`[RealTimeSyncService] Clearing ${key} sync interval`);
        clearInterval(this.syncIntervals[key]);
        this.syncIntervals[key] = null;
      }
    });

    console.log('[RealTimeSyncService] All sync intervals stopped');
  }

  /**
   * Set app active state
   */
  static setAppActive(isActive) {
    console.log('[RealTimeSyncService] App active state changed:', isActive);
    const previousState = this.isAppActive;
    this.isAppActive = isActive;

    // Don't start sync intervals if service is disabled
    if (!this.SYNC_ENABLED) {
      console.log('[RealTimeSyncService] ⚠️ Sync service is disabled via environment variable, skipping app active state changes');
      return;
    }

    if (isActive && this.userId && !previousState) {
      // Start sync intervals when app becomes active (only if we have a logged-in user)
      console.log('[RealTimeSyncService] App became active with logged-in user, starting sync intervals');
      this.startUserProgressSync();
      this.startAIChatSync();
    } else if (!isActive && previousState) {
      // Stop sync intervals when app becomes inactive
      console.log('[RealTimeSyncService] App became inactive, stopping sync intervals');
      this.stopSyncIntervals();
    } else if (isActive && !this.userId) {
      console.log('[RealTimeSyncService] App became active but no user is logged in, sync intervals will not start');
    }
  }

  /**
   * Start user progress sync interval
   */
  static startUserProgressSync() {
    if (!this.SYNC_ENABLED) {
      console.log('[RealTimeSyncService] ⚠️ User progress sync disabled via environment variable');
      return;
    }

    if (this.syncIntervals.userProgress) {
      console.log('[RealTimeSyncService] Clearing existing user progress sync interval');
      clearInterval(this.syncIntervals.userProgress);
    }

    console.log(`[RealTimeSyncService] Starting user progress sync every ${this.USER_PROGRESS_SYNC_WINDOW} seconds`);

    this.syncIntervals.userProgress = setInterval(async () => {
      console.log('[RealTimeSyncService] ===== USER PROGRESS SYNC INTERVAL TRIGGERED =====');
      console.log('[RealTimeSyncService] App active:', this.isAppActive, 'User ID:', this.userId);
      console.log('[RealTimeSyncService] Sync window:', this.USER_PROGRESS_SYNC_WINDOW, 'seconds');

      try {
        if (this.isAppActive && this.userId) {
          console.log('[RealTimeSyncService] Executing user progress sync...');
          await this.syncUserProgressToMongoDB();
          console.log('[RealTimeSyncService] User progress sync completed successfully');
        } else {
          console.log('[RealTimeSyncService] Skipping user progress sync - app inactive or no user ID');
          console.log('[RealTimeSyncService] Prerequisites:', {
            isAppActive: this.isAppActive,
            hasUserId: !!this.userId,
            userId: this.userId
          });
        }
      } catch (error) {
        console.error('[RealTimeSyncService] Error in user progress sync interval:', error);
      }
      console.log('[RealTimeSyncService] ===== USER PROGRESS SYNC INTERVAL COMPLETE =====');
    }, this.USER_PROGRESS_SYNC_WINDOW * 1000);

    console.log('[RealTimeSyncService] User progress sync interval created successfully');
  }

  /**
   * Start AI chat sync interval
   */
  static startAIChatSync() {
    if (!this.SYNC_ENABLED) {
      console.log('[RealTimeSyncService] ⚠️ AI chat sync disabled via environment variable');
      return;
    }

    if (this.syncIntervals.aiChat) {
      console.log('[RealTimeSyncService] Clearing existing AI chat sync interval');
      clearInterval(this.syncIntervals.aiChat);
    }

    console.log(`[RealTimeSyncService] Starting AI chat sync every ${this.AI_CHAT_SYNC_WINDOW} seconds`);

    this.syncIntervals.aiChat = setInterval(async () => {
      console.log('[RealTimeSyncService] ===== AI CHAT SYNC INTERVAL TRIGGERED =====');
      console.log('[RealTimeSyncService] App active:', this.isAppActive, 'User ID:', this.userId);
      console.log('[RealTimeSyncService] Sync window:', this.AI_CHAT_SYNC_WINDOW, 'seconds');

      try {
        if (this.isAppActive && this.userId) {
          console.log('[RealTimeSyncService] Executing AI chat sync...');
          await this.syncAIChatToMongoDB();
          console.log('[RealTimeSyncService] AI chat sync completed successfully');
        } else {
          console.log('[RealTimeSyncService] Skipping AI chat sync - app inactive or no user ID');
          console.log('[RealTimeSyncService] Prerequisites:', {
            isAppActive: this.isAppActive,
            hasUserId: !!this.userId,
            userId: this.userId
          });
        }
      } catch (error) {
        console.error('[RealTimeSyncService] Error in AI chat sync interval:', error);
      }
      console.log('[RealTimeSyncService] ===== AI CHAT SYNC INTERVAL COMPLETE =====');
    }, this.AI_CHAT_SYNC_WINDOW * 1000);

    console.log('[RealTimeSyncService] AI chat sync interval created successfully');
  }

  /**
   * Sync user progress to MongoDB if there are changes
   * Uses the bulk update API endpoint: PUT /api/user/progress/{examId}
   */
  static async syncUserProgressToMongoDB() {
    try {
      if (!(await this.validateSyncPrerequisites('user progress sync'))) {
        return;
      }

      // Get current user progress from AsyncStorage (use same key as UserProgressContext)
      const currentProgressData = await AsyncStorage.getItem('userProgress');

      if (!currentProgressData) {
        console.log('[RealTimeSyncService] No user progress data found in AsyncStorage');
        return;
      }

      const currentProgress = JSON.parse(currentProgressData);

      // Enhanced change detection with detailed logging
      const currentProgressHash = JSON.stringify(currentProgress);

      console.log('[RealTimeSyncService] ===== USER PROGRESS SYNC CHECK =====');
      console.log('[RealTimeSyncService] Current progress data keys:', Object.keys(currentProgress));
      console.log('[RealTimeSyncService] Current progress hash length:', currentProgressHash.length);
      console.log('[RealTimeSyncService] Last sync hash length:', this.lastSyncData.userProgress?.length || 0);

      if (this.lastSyncData.userProgress === currentProgressHash) {
        console.log('[RealTimeSyncService] User progress unchanged, skipping sync');
        return;
      }

      console.log('[RealTimeSyncService] ===== SYNCING USER PROGRESS TO MONGODB =====');
      console.log('[RealTimeSyncService] Progress data changed, syncing to MongoDB');
      console.log('[RealTimeSyncService] Number of exams with progress:', Object.keys(currentProgress).length);

      let syncedExams = 0;
      let failedExams = 0;

      // Sync each exam's progress using the new bulk API
      for (const [examId, examProgress] of Object.entries(currentProgress)) {
        try {
          console.log(`[RealTimeSyncService] Syncing progress for exam ${examId}:`, {
            subjects: Object.keys(examProgress).length,
            subjectNames: Object.keys(examProgress)
          });

          // examProgress is already in the correct format for the new API:
          // { [subject]: { browsed: [], bookmarked: [], correct: [], incorrect: [] } }

          // Log sample data for debugging
          const firstSubject = Object.keys(examProgress)[0];
          if (firstSubject && examProgress[firstSubject]) {
            console.log(`[RealTimeSyncService] Sample data for subject "${firstSubject}":`, {
              browsed: examProgress[firstSubject].browsed?.length || 0,
              bookmarked: examProgress[firstSubject].bookmarked?.length || 0,
              correct: examProgress[firstSubject].correct?.length || 0,
              incorrect: examProgress[firstSubject].incorrect?.length || 0
            });
          }

          // Use the bulk update API with the correct data format
          await apiClient.updateUserProgressBulk(examId, examProgress);

          syncedExams++;
          console.log(`[RealTimeSyncService] ✅ Progress synced successfully for exam: ${examId}`);
        } catch (examError) {
          failedExams++;
          this.handleSyncError(`progress sync for exam ${examId}`, examError, examProgress);
        }
      }

      // Update last sync data only if at least one exam synced successfully
      if (syncedExams > 0) {
        this.lastSyncData.userProgress = currentProgressHash;
        console.log(`[RealTimeSyncService] User progress sync completed: ${syncedExams} synced, ${failedExams} failed`);
      } else {
        console.error(`[RealTimeSyncService] All progress sync attempts failed: ${failedExams} exams`);
      }

    } catch (error) {
      this.handleSyncError('user progress sync', error);
    }
  }

  /**
   * Sync AI chat data to MongoDB if there are changes
   * Uses the new ApiClient methods for AI chat operations
   */
  static async syncAIChatToMongoDB() {
    try {
      if (!(await this.validateSyncPrerequisites('AI chat sync'))) {
        return;
      }

      console.log('[RealTimeSyncService] ===== AI CHAT SYNC STARTED =====');
      console.log('[RealTimeSyncService] Checking AsyncStorage for AI chat data...');

      // Get current AI chat data from AsyncStorage (use same key as AIChatContext)
      const currentChatData = await AsyncStorage.getItem('aiChatHistories');

      console.log('[RealTimeSyncService] AsyncStorage result:', {
        hasData: !!currentChatData,
        dataLength: currentChatData?.length || 0,
        dataPreview: currentChatData ? currentChatData.substring(0, 100) + '...' : 'null'
      });

      if (!currentChatData) {
        console.log('[RealTimeSyncService] No AI chat data found in AsyncStorage - creating test data for verification');

        // Create test AI chat data to verify sync is working
        const testChatData = {
          'aws-certified-ai-practitioner-aif-c01': {
            '679e05c962fb0d0bbe484926': {
              messages: [
                {
                  id: 'welcome-message',
                  choices: [{
                    index: 0,
                    message: {
                      role: 'assistant',
                      content: 'I\'m here to help with this Q&A question. What would you like to know?',
                      isLoading: false
                    }
                  }]
                }
              ],
              quickReplies: [
                'Explain why the answer is correct and others are wrong.',
                'Give an example to clarify the answer.',
                'Share references for the correct answer.'
              ]
            }
          }
        };

        // Save test data to AsyncStorage
        await AsyncStorage.setItem('aiChatHistories', JSON.stringify(testChatData));
        console.log('[RealTimeSyncService] Test AI chat data created and saved to AsyncStorage');

        // Use test data for sync
        const currentChats = testChatData;
        const currentChatHash = JSON.stringify(currentChats);

        console.log('[RealTimeSyncService] ===== AI CHAT SYNC CHECK (TEST DATA) =====');
        console.log('[RealTimeSyncService] Test chat data keys:', Object.keys(currentChats));
        console.log('[RealTimeSyncService] Test chat hash length:', currentChatHash.length);
        console.log('[RealTimeSyncService] Last sync hash length:', this.lastSyncData.aiChat?.length || 0);

        // Force sync test data
        console.log('[RealTimeSyncService] ===== SYNCING TEST AI CHAT DATA TO MONGODB =====');
        console.log('[RealTimeSyncService] Test AI chat data will be synced to MongoDB');

        await this.performAIChatSync(currentChats, currentChatHash);
        return;
      }

      const currentChats = JSON.parse(currentChatData);

      // Check if data has changed since last sync
      const currentChatHash = JSON.stringify(currentChats);

      console.log('[RealTimeSyncService] ===== AI CHAT SYNC CHECK =====');
      console.log('[RealTimeSyncService] Current chat data keys:', Object.keys(currentChats));
      console.log('[RealTimeSyncService] Current chat hash length:', currentChatHash.length);
      console.log('[RealTimeSyncService] Last sync hash length:', this.lastSyncData.aiChat?.length || 0);
      console.log('[RealTimeSyncService] Hash comparison:', {
        current: currentChatHash.substring(0, 50) + '...',
        last: this.lastSyncData.aiChat ? this.lastSyncData.aiChat.substring(0, 50) + '...' : 'null',
        areEqual: this.lastSyncData.aiChat === currentChatHash
      });

      if (this.lastSyncData.aiChat === currentChatHash) {
        console.log('[RealTimeSyncService] AI chat data unchanged, skipping sync');
        return;
      }

      console.log('[RealTimeSyncService] ===== SYNCING AI CHAT DATA TO MONGODB =====');
      console.log('[RealTimeSyncService] AI chat data changed, syncing to MongoDB');

      await this.performAIChatSync(currentChats, currentChatHash);

    } catch (error) {
      this.handleSyncError('AI chat sync', error);
    }
  }

  /**
   * Perform the actual AI chat sync operation
   */
  static async performAIChatSync(currentChats, currentChatHash) {
    try {
      let syncedChats = 0;
      let failedChats = 0;

      // Format the complete chat history for the API
      // The API expects the data organized by question IDs, not by exam/qna structure
      const formattedChatHistory = {};

      // Convert from exam/qna structure to questionId structure
      for (const [examCode, examChats] of Object.entries(currentChats)) {
        for (const [qnaId, chatData] of Object.entries(examChats)) {
          // Use a combination of examCode and qnaId as the questionId
          const questionId = `${examCode}_${qnaId}`;

          formattedChatHistory[questionId] = {
            messages: chatData.messages || [],
            quickReplies: chatData.quickReplies || [],
            metadata: {
              examCode: examCode,
              qnaId: qnaId,
              lastUpdated: new Date().toISOString(),
              syncSource: 'real_time_sync'
            }
          };

          console.log(`[RealTimeSyncService] Formatted AI chat for questionId: ${questionId}`, {
            messagesCount: chatData.messages?.length || 0,
            quickRepliesCount: chatData.quickReplies?.length || 0,
            examCode: examCode,
            qnaId: qnaId
          });
        }
      }

      console.log('[RealTimeSyncService] Syncing complete AI chat history to MongoDB', {
        totalQuestions: Object.keys(formattedChatHistory).length,
        questionIds: Object.keys(formattedChatHistory)
      });

      // Use the ApiClient method to add AI chat history
      console.log('[RealTimeSyncService] Calling apiClient.addAIChatHistory...');

      // The addAIChatHistory method expects examId, qnaId, messages, and quickReplies
      // We need to call it for each exam/qna combination
      let syncedCount = 0;
      for (const [examCode, examChats] of Object.entries(currentChats)) {
        for (const [qnaId, chatData] of Object.entries(examChats)) {
          try {
            console.log(`[RealTimeSyncService] Syncing AI chat for ${examCode}/${qnaId}...`);
            const response = await apiClient.addAIChatHistory(
              examCode,
              qnaId,
              chatData.messages || [],
              chatData.quickReplies || []
            );
            console.log(`[RealTimeSyncService] ✅ AI chat synced for ${examCode}/${qnaId}:`, response);
            syncedCount++;
          } catch (chatError) {
            console.error(`[RealTimeSyncService] ❌ Failed to sync AI chat for ${examCode}/${qnaId}:`, chatError);
            throw chatError; // Re-throw to be caught by outer try-catch
          }
        }
      }

      syncedChats = syncedCount;
      console.log(`[RealTimeSyncService] ✅ AI chat history synced successfully: ${syncedChats} chats`);

      // Update last sync data only if sync was successful
      this.lastSyncData.aiChat = currentChatHash;
      console.log(`[RealTimeSyncService] AI chat sync completed: ${syncedChats} synced, ${failedChats} failed`);

    } catch (chatError) {
      const totalChats = Object.keys(currentChats).reduce((total, examCode) => {
        return total + Object.keys(currentChats[examCode]).length;
      }, 0);

      failedChats = totalChats;
      this.handleSyncError('AI chat history sync', chatError, currentChats);
      console.error(`[RealTimeSyncService] All AI chat sync attempts failed: ${failedChats} chats`);
    }
  }

  /**
   * Immediately sync quiz result to MongoDB (real-time)
   */
  static async syncQuizResultToMongoDB(quizResult) {
    try {
      console.log('[RealTimeSyncService] ===== SYNCING QUIZ RESULT TO MONGODB (REAL-TIME) =====');
      console.log('[RealTimeSyncService] Quiz result data:', {
        id: quizResult.id,
        examCode: quizResult.examCode,
        score: quizResult.score,
        correctAnswers: quizResult.correctAnswers,
        totalQuestions: quizResult.totalQuestions,
        date: quizResult.date,
        answersCount: Object.keys(quizResult.answers || {}).length,
        questionsCount: quizResult.questions?.length || 0
      });

      // Format answers array with all required fields
      const formattedAnswers = [];
      const userAnswers = quizResult.answers || {};
      const questions = quizResult.questions || [];
      const flaggedIds = new Set(quizResult.flaggedIds || []);

      // Process each question to create properly formatted answer objects
      questions.forEach((question, index) => {
        const questionId = question.id || question._id || question.originalIndex || index;
        const originalIndex = question.originalIndex !== undefined ? question.originalIndex : index;
        const userAnswer = userAnswers[originalIndex] || userAnswers[questionId] || [];
        const correctAnswer = question.answer || [];

        // Determine if the answer is correct
        let isCorrect = false;
        if (Array.isArray(userAnswer) && Array.isArray(correctAnswer)) {
          // For multiple choice, check if arrays match
          isCorrect = userAnswer.length === correctAnswer.length &&
                     userAnswer.every(ans => correctAnswer.includes(ans));
        } else if (userAnswer && correctAnswer) {
          // For single choice, direct comparison
          isCorrect = userAnswer.toString() === correctAnswer.toString();
        }

        // Calculate time taken (estimate based on total time and question count)
        const estimatedTimePerQuestion = (quizResult.timeSpent || 0) / questions.length;
        const timeTaken = Math.round(estimatedTimePerQuestion);

        const answerObject = {
          questionId: questionId.toString(),
          selectedOption: Array.isArray(userAnswer) ? userAnswer : [userAnswer].filter(Boolean),
          isCorrect: isCorrect,
          timeTaken: timeTaken,
          isFlagged: flaggedIds.has(originalIndex) || flaggedIds.has(questionId),
          questionIndex: index,
          originalIndex: originalIndex
        };

        formattedAnswers.push(answerObject);
      });

      console.log('[RealTimeSyncService] Formatted answers sample:', {
        totalAnswers: formattedAnswers.length,
        sampleAnswer: formattedAnswers[0] || null,
        correctAnswersCount: formattedAnswers.filter(a => a.isCorrect).length,
        flaggedAnswersCount: formattedAnswers.filter(a => a.isFlagged).length
      });

      // Format quiz result for MongoDB API with properly structured answers
      const mongoQuizResult = {
        quizId: quizResult.examCode || quizResult.id,
        examCode: quizResult.examCode,
        score: quizResult.score,
        totalQuestions: quizResult.totalQuestions,
        correctAnswers: quizResult.correctAnswers,
        timeTaken: quizResult.timeSpent || 0,
        completedAt: quizResult.date || new Date().toISOString(),
        answers: formattedAnswers, // Properly formatted answers array
        flaggedQuestions: Array.from(flaggedIds),
        userId: this.userId,
        resultId: quizResult.id,
        // Additional metadata
        metadata: {
          syncedAt: new Date().toISOString(),
          syncSource: 'real_time_sync',
          clientVersion: '1.0.0'
        }
      };

      console.log('[RealTimeSyncService] MongoDB-formatted quiz result:', {
        quizId: mongoQuizResult.quizId,
        examCode: mongoQuizResult.examCode,
        score: mongoQuizResult.score,
        totalQuestions: mongoQuizResult.totalQuestions,
        correctAnswers: mongoQuizResult.correctAnswers,
        timeTaken: mongoQuizResult.timeTaken,
        answersCount: mongoQuizResult.answers.length,
        flaggedCount: mongoQuizResult.flaggedQuestions.length,
        userId: mongoQuizResult.userId
      });

      // Submit quiz result to MongoDB
      const response = await apiClient.submitQuizResult(mongoQuizResult);

      console.log('[RealTimeSyncService] Quiz result synced to MongoDB successfully:', response);
      return response;

    } catch (error) {
      console.error('[RealTimeSyncService] Error syncing quiz result to MongoDB:', error);
      console.error('[RealTimeSyncService] Quiz result sync error details:', {
        message: error.message,
        status: error.status,
        response: error.response
      });
      throw error;
    }
  }

  /**
   * Immediately sync purchase to MongoDB (real-time)
   */
  static async syncPurchaseToMongoDB(purchaseData) {
    try {
      console.log('[RealTimeSyncService] ===== SYNCING PURCHASE TO MONGODB (REAL-TIME) =====');
      console.log('[RealTimeSyncService] Original purchase data:', purchaseData);

      // Determine purchase amount based on purchase type
      let purchaseAmount = 5.00; // Default amount for regular purchases
      if (purchaseData.examId === 'ai_credits' || purchaseData.purchaseType === 'ai_credits') {
        purchaseAmount = 5.00; // AI credits package price
      } else if (purchaseData.isTestPurchase) {
        purchaseAmount = 5.00; // Test purchase price
      }

      // Create properly formatted purchase object for MongoDB
      const mongodbPurchaseData = {
        productId: purchaseData.examId || purchaseData.productId || 'unknown_product',
        transactionId: purchaseData.transactionId || `local_${Date.now()}`,
        platform: 'android', // Valid enum value: 'ios', 'android', or 'web'
        currency: 'USD', // Required field
        amount: purchaseAmount, // Required field - purchase price in USD
        receipt: JSON.stringify(purchaseData),
        purchaseData: {
          ...purchaseData,
          currency: 'USD',
          amount: purchaseAmount,
          platform: 'android'
        }
      };

      console.log('[RealTimeSyncService] MongoDB-formatted purchase data:', {
        productId: mongodbPurchaseData.productId,
        transactionId: mongodbPurchaseData.transactionId,
        platform: mongodbPurchaseData.platform,
        currency: mongodbPurchaseData.currency,
        amount: mongodbPurchaseData.amount,
        purchaseType: purchaseData.purchaseType || 'regular',
        examId: purchaseData.examId,
        isTestPurchase: purchaseData.isTestPurchase || false
      });

      // Call API to save purchase to MongoDB
      const response = await apiClient.verifyPurchase(mongodbPurchaseData);

      console.log('[RealTimeSyncService] Purchase synced to MongoDB successfully:', response);
      return response;
    } catch (error) {
      console.error('[RealTimeSyncService] Error syncing purchase to MongoDB:', error);
      console.error('[RealTimeSyncService] Purchase sync error details:', {
        message: error.message,
        status: error.status,
        response: error.response
      });
      throw error;
    }
  }

  /**
   * Immediately sync AI credit purchase to MongoDB (real-time)
   */
  static async syncAICreditPurchaseToMongoDB(creditData) {
    try {
      console.log('[RealTimeSyncService] ===== SYNCING AI CREDIT PURCHASE TO MONGODB (REAL-TIME) =====');
      console.log('[RealTimeSyncService] Original AI credit data:', creditData);

      // Determine credit amount and price
      const creditAmount = creditData.amount || creditData.creditAmount || 10;
      const purchasePrice = 5.00; // Standard AI credit package price

      // Create properly formatted AI credit purchase object for MongoDB
      const mongodbCreditData = {
        amount: creditAmount, // Number of credits
        paymentMethod: creditData.paymentMethod || 'test_purchase',
        currency: 'USD', // Required field
        price: purchasePrice, // Required field - purchase price in USD
        platform: 'android', // Valid enum value
        paymentDetails: {
          transactionId: creditData.transactionId || `ai_credit_${Date.now()}`,
          price: `$${purchasePrice} USD`,
          purchaseDate: creditData.purchaseDate || new Date().toISOString(),
          uniqueId: creditData.uniqueId,
          currency: 'USD',
          amount: purchasePrice,
          platform: 'android',
          examId: creditData.examId || 'ai_credits'
        }
      };

      console.log('[RealTimeSyncService] MongoDB-formatted AI credit data:', {
        creditAmount: mongodbCreditData.amount,
        paymentMethod: mongodbCreditData.paymentMethod,
        currency: mongodbCreditData.currency,
        price: mongodbCreditData.price,
        platform: mongodbCreditData.platform,
        transactionId: mongodbCreditData.paymentDetails.transactionId,
        examId: mongodbCreditData.paymentDetails.examId
      });

      // Call API to purchase AI credits and save to MongoDB
      const response = await apiClient.purchaseAICredits(mongodbCreditData);

      console.log('[RealTimeSyncService] AI credit purchase synced to MongoDB successfully:', response);
      return response;
    } catch (error) {
      console.error('[RealTimeSyncService] Error syncing AI credit purchase to MongoDB:', error);
      console.error('[RealTimeSyncService] AI credit sync error details:', {
        message: error.message,
        status: error.status,
        response: error.response
      });
      throw error;
    }
  }

  /**
   * Sync AI credit balance update to MongoDB in real-time
   */
  static async syncAICreditBalanceToMongoDB(userId, newBalance, transaction) {
    try {
      console.log('[RealTimeSyncService] ===== SYNCING AI CREDIT BALANCE TO MONGODB =====');
      console.log('[RealTimeSyncService] Syncing balance update:', {
        userId,
        newBalance,
        transaction
      });

      // For now, we'll use the purchase API to record the transaction
      // In a real implementation, you might have a separate endpoint for balance updates
      if (transaction && transaction.type === 'purchase') {
        return await this.syncAICreditPurchaseToMongoDB(transaction);
      }

      console.log('[RealTimeSyncService] AI credit balance sync completed');
      return { success: true, balance: newBalance };
    } catch (error) {
      console.error('[RealTimeSyncService] Error syncing AI credit balance to MongoDB:', error);
      throw error;
    }
  }

  /**
   * Centralized error handler for sync operations
   */
  static handleSyncError(operation, error, data = null) {
    const errorInfo = {
      operation,
      timestamp: new Date().toISOString(),
      userId: this.userId,
      error: {
        message: error.message,
        name: error.name,
        stack: error.stack?.substring(0, 1000),
        status: error.status || error.response?.status,
        statusText: error.statusText || error.response?.statusText,
        responseData: error.response?.data,
        url: error.config?.url,
        method: error.config?.method,
        code: error.code,
        errno: error.errno,
        syscall: error.syscall,
        timeout: error.timeout,
        isAxiosError: error.isAxiosError
      },
      data: data ? (typeof data === 'string' ? data.substring(0, 500) : JSON.stringify(data).substring(0, 500)) : null,
      networkInfo: {
        isAppActive: this.isAppActive,
        hasUserId: !!this.userId,
        syncIntervals: {
          userProgress: !!this.syncIntervals.userProgress,
          aiChat: !!this.syncIntervals.aiChat
        },
        lastSyncHashes: {
          userProgress: !!this.lastSyncData.userProgress,
          aiChat: !!this.lastSyncData.aiChat
        }
      }
    };

    console.error(`[RealTimeSyncService] Sync operation failed: ${operation}`, errorInfo);

    // Log the complete error object for debugging
    console.error(`[RealTimeSyncService] Complete error object for ${operation}:`, error);

    // Specific error type handling
    if (error.code === 'NETWORK_ERROR' || error.message?.includes('Network')) {
      console.error(`[RealTimeSyncService] 🌐 Network error detected during ${operation}`);
    }

    if (error.status === 401 || error.status === 403) {
      console.error(`[RealTimeSyncService] 🔐 Authentication error during ${operation} - token may be expired`);
    }

    if (error.status === 500 || error.status === 502 || error.status === 503) {
      console.error(`[RealTimeSyncService] 🔥 Server error during ${operation} - backend may be down`);
    }

    if (error.timeout || error.code === 'ECONNABORTED') {
      console.error(`[RealTimeSyncService] ⏱️ Timeout error during ${operation}`);
    }

    // In a production environment, you might want to send this to a logging service
    // LoggingService.logError('SYNC_OPERATION_FAILED', errorInfo);

    return errorInfo;
  }

  /**
   * Check network connectivity by making a simple API request
   */
  static async checkNetworkConnectivity() {
    try {
      // Make a simple HEAD request to check connectivity
      // Using a lightweight endpoint or the base API URL
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 5000); // 5 second timeout

      const response = await fetch('https://httpbin.org/status/200', {
        method: 'HEAD',
        signal: controller.signal,
        cache: 'no-cache'
      });

      clearTimeout(timeoutId);
      const isConnected = response.ok;

      console.log(`[RealTimeSyncService] Network connectivity check:`, {
        isConnected,
        status: response.status,
        method: 'API_REQUEST_TEST'
      });

      return isConnected;
    } catch (error) {
      console.log(`[RealTimeSyncService] Network connectivity check failed:`, {
        isConnected: false,
        error: error.message,
        method: 'API_REQUEST_TEST'
      });

      // If network check fails, assume we have connectivity and let the actual sync operations handle network errors
      // This prevents blocking sync operations due to connectivity check failures
      return true;
    }
  }

  /**
   * Validate sync prerequisites including network connectivity
   */
  static async validateSyncPrerequisites(operation) {
    console.log(`[RealTimeSyncService] Validating prerequisites for ${operation}:`, {
      userId: this.userId,
      isAppActive: this.isAppActive
    });

    if (!this.userId) {
      console.error(`[RealTimeSyncService] Cannot perform ${operation}: No user ID available`);
      return false;
    }

    if (!this.isAppActive) {
      console.warn(`[RealTimeSyncService] Skipping ${operation}: App is not active`);
      return false;
    }

    // Check network connectivity
    const isConnected = await this.checkNetworkConnectivity();
    if (!isConnected) {
      console.warn(`[RealTimeSyncService] Cannot perform ${operation}: No network connectivity`);
      return false;
    }

    console.log(`[RealTimeSyncService] Prerequisites validated for ${operation}`);
    return true;
  }

  /**
   * Force sync all data immediately
   */
  static async forceSyncAll() {
    console.log('[RealTimeSyncService] ===== FORCE SYNCING ALL DATA =====');

    if (!(await this.validateSyncPrerequisites('force sync all'))) {
      return;
    }

    try {
      // Reset last sync data to force sync
      this.lastSyncData = {
        userProgress: null,
        aiChat: null,
        quizResults: null
      };

      // Force sync both user progress and AI chat
      console.log('[RealTimeSyncService] Forcing user progress and AI chat sync...');
      const syncResults = await Promise.allSettled([
        this.syncUserProgressToMongoDB(),
        this.syncAIChatToMongoDB()
      ]);

      // Log the results of the force sync
      syncResults.forEach((result, index) => {
        const syncType = index === 0 ? 'user progress' : 'AI chat';
        if (result.status === 'fulfilled') {
          console.log(`[RealTimeSyncService] Force sync ${syncType}: ✅ successful`);
        } else {
          console.error(`[RealTimeSyncService] Force sync ${syncType}: ❌ failed:`, result.reason);
        }
      });

      console.log('[RealTimeSyncService] Force sync completed');
    } catch (error) {
      this.handleSyncError('force sync all', error);
    }
  }

  /**
   * Get sync status
   */
  static getSyncStatus() {
    const status = {
      isActive: this.isAppActive,
      userId: this.userId,
      intervals: {
        userProgress: !!this.syncIntervals.userProgress,
        aiChat: !!this.syncIntervals.aiChat
      },
      syncWindows: {
        userProgress: this.USER_PROGRESS_SYNC_WINDOW,
        aiChat: this.AI_CHAT_SYNC_WINDOW
      },
      lastSyncHashes: {
        userProgress: !!this.lastSyncData.userProgress,
        aiChat: !!this.lastSyncData.aiChat
      }
    };

    console.log('[RealTimeSyncService] Current sync status:', status);
    return status;
  }

  /**
   * Log detailed sync status for debugging
   */
  static logSyncStatus() {
    console.log('[RealTimeSyncService] ===== DETAILED SYNC STATUS =====');
    console.log('[RealTimeSyncService] App Active:', this.isAppActive);
    console.log('[RealTimeSyncService] User ID:', this.userId);
    console.log('[RealTimeSyncService] Sync Intervals Active:', {
      userProgress: !!this.syncIntervals.userProgress,
      aiChat: !!this.syncIntervals.aiChat
    });
    console.log('[RealTimeSyncService] Sync Windows (seconds):', {
      userProgress: this.USER_PROGRESS_SYNC_WINDOW,
      aiChat: this.AI_CHAT_SYNC_WINDOW
    });
    console.log('[RealTimeSyncService] Last Sync Data Available:', {
      userProgress: !!this.lastSyncData.userProgress,
      aiChat: !!this.lastSyncData.aiChat,
      quizResults: !!this.lastSyncData.quizResults
    });
    console.log('[RealTimeSyncService] ===== END SYNC STATUS =====');
  }


}

export default RealTimeSyncService;
