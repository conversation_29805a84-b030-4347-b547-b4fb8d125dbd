import React, { createContext, useContext, useState, useEffect } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
//import RealTimeSyncService from '../services/RealTimeSyncService';

const QuizResultContext = createContext();

export const QuizResultProvider = ({ children }) => {
    const [results, setResults] = useState([]);

    // Load results on mount
    useEffect(() => {
        const loadResults = async () => {
            try {
                const jsonValue = await AsyncStorage.getItem('@quizResults');
                if (jsonValue !== null) {
                    setResults(JSON.parse(jsonValue));
                }
            } catch (e) {
                console.error('Failed to load results:', e);
            }
        };
        loadResults();
    }, []);

    // Save results whenever they change
    useEffect(() => {
        const saveResults = async () => {
            try {
                const jsonValue = JSON.stringify(results);
                await AsyncStorage.setItem('@quizResults', jsonValue);
            } catch (e) {
                console.error('Failed to save results:', e);
            }
        };
        saveResults();
    }, [results]);

    const addResult = async (result) => {
        console.log('[QuizResultContext] Adding quiz result:', {
            id: result.id,
            examCode: result.examCode,
            score: result.score,
            correctAnswers: result.correctAnswers,
            totalQuestions: result.totalQuestions
        });

        const newResult = {
            id: result.id,
            examCode: result.examCode,
            score: result.score,
            correctAnswers: result.correctAnswers,
            totalQuestions: result.totalQuestions,
            date: result.date,
            timeSpent: result.timeSpent,
            flaggedIds: result.flaggedIds,
            answers: result.answers,
            questions: result.questions
        };

        // Update local state
        setResults(prev => {
            const newResults = [newResult, ...prev];
            return newResults;
        });

        // Sync quiz result to MongoDB in real-time
        /* try {
            console.log('[QuizResultContext] Syncing quiz result to MongoDB...');
            const mongoResponse = await RealTimeSyncService.syncQuizResultToMongoDB(newResult);
            console.log('[QuizResultContext] Quiz result synced to MongoDB successfully:', mongoResponse);

            // Update the result with MongoDB response data if available
            if (mongoResponse && mongoResponse._id) {
                newResult.mongoId = mongoResponse._id;
                newResult.syncedAt = new Date().toISOString();
            }
        } catch (syncError) {
            console.error('[QuizResultContext] Failed to sync quiz result to MongoDB:', syncError);
            // Continue with local storage even if MongoDB sync fails
            newResult.syncError = syncError.message;
            newResult.syncedAt = null;
        } */

        return newResult;
    };

    return (
        <QuizResultContext.Provider value={{ results, addResult }}>
            {children}
        </QuizResultContext.Provider>
    );
};

export const useQuizResult = () => {
    const context = useContext(QuizResultContext);
    if (!context) {
        throw new Error('useQuizResult must be used within a QuizResultProvider');
    }
    return context;
};